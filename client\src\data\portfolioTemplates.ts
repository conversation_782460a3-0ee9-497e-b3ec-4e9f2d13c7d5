// Define the Template Asset type for portfolio templates
export interface TemplateAsset {
  id?: string; // Numeric ID from API
  symbol: string;
  fullName: string;
  percentage: number; // Allocation percentage (0-100)
  color: string;
  score?: number; // CoinScout score
  description?: string;
  // Template-specific fields
  marketCap?: 'Large-Cap' | 'Mid-Cap' | 'Small-Cap';
  categories?: string[]; // Multiple categories support
  primaryCategory?: string; // Main category for display
  // Value will be calculated dynamically based on portfolio budget
  value?: number; // Optional, calculated at runtime
}

/**
 * Portfolio templates provide pre-defined allocations that users can start with.
 * Each template represents a specific investment strategy or approach.
 */

export interface PortfolioTemplate {
  id: string;
  name: string;
  description: string;
  riskLevel: 'low' | 'medium' | 'high';
  timeframe: 'short' | 'medium' | 'long';
  volatility: string;
  allocation: TemplateAsset[];
  suitableFor: string[];
  notSuitableFor: string[];
  strategy: string;
  likeCount: number;
  isLikedByUser: boolean;
  isRecommended?: boolean;
  totalScore?: number;
  metrics?: {
    portfolio_quality?: { score: number; contribution: number; maxScore: number };
    category_diversification?: { score: number; contribution: number; maxScore: number };
    trend_category?: { score: number; contribution: number; maxScore: number };
    marketCapDistribution?: { score: number; contribution: number; maxScore: number };
    stablecoinRatio?: { score: number; contribution: number; maxScore: number };
    portfolio_weight?: { score: number; contribution: number; maxScore: number };
  };
  // Additional data for detailed analysis
  market_cap_distribution?: {
    'Large-Cap': number;
    'Mid-Cap': number;
    'Small-Cap': number;
  };
  trending_categories?: Array<{
    name: string;
    allocation: number;
    coins: Array<{ symbol: string; name: string }>;
  }>;
  stablecoin_ratio?: {
    stablecoin_analysis: {
      current_ratio: number;
      current_value: number;
      target_ratio: number;
      target_value: number;
      recommended_range: { min: number; max: number };
      status: 'well_balanced' | 'needs_more_stability' | 'overexposed';
    };
  };
}

// Templates will be fetched from API - no mock data needed