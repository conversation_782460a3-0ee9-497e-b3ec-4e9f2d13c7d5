import React, { useState, useEffect, useRef } from 'react';
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON>, CardContent, CardHeader as OriginalCardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TotalScoreBadge } from "@/components/TotalScoreBadge";
import {
  Section,
  Grid,
  StyledCard,
  MetricCard,
  ColoredIconContainer,
  GradientText,
  ActionButton,
  StatusBadge,
  NumberedHeader,
  TimePeriodButton,
  TimePeriodButtonGroup,
  FormInput,
  FormSelect,
  FormLabel
} from "@/components/shared";
import { COMPONENT_STYLES, COLOR_VARIANTS, ANIMATION_STYLES, TEXT_STYLES, LAYOUT_STYLES } from "@/styles/constants";
import {
  Save,
  Share2,
  FileDown,
  <PERSON>older<PERSON><PERSON>,
  TrendingDown,
  BarChart2,
  BarChart3,
  Info,
  AlertTriangle,
  TrendingUp,
  DollarSign,
  LineChart as ChartIcon,
  Pencil,
  Plus,
  ActivitySquare,
  ShieldCheck,
  ChartLine,
  Clock,
  Shield,
  Flame,
  XCircle,
  Sparkles,
  Settings,
  CheckCircle,
  X,
  ChevronRight,
  RotateCcw,
  Brain,
  Trash2,
  Edit2,
  MoreVertical,
  ArrowUpRight,
  Database,
  ChevronDown
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, Legend, LineChart as RechartsLineChart, Line } from 'recharts';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { StandardTooltip } from "@/components/shared/StandardTooltip";
import {
  RiskProfileType,
  PortfolioSuggestion,
  PortfolioTheme,
  PortfolioAsset,
} from '@/lib/types';
import {
  getPortfolioSuggestion,
  analyzePortfolio,
  riskProfiles
} from '@/lib/portfolioData';
import { InvestmentTutorial } from "@/components/InvestmentTutorial";
import { cn } from "@/lib/utils";
import { useAuth } from "@/hooks/use-auth";
import { useLanguage } from "@/contexts/LanguageContext";
import { LoginPromptButton } from "@/components/auth/LoginPromptButton";
import { UnifiedPortfolioSummary } from '@/components/shared/UnifiedPortfolioSummary';
import { UnifiedPortfolioSummaryRedesigned } from '@/components/shared/UnifiedPortfolioSummaryRedesigned';
import { UnifiedAllocationChart } from "@/components/shared/UnifiedAllocationChart";
import { UnifiedMetricCard } from "@/components/shared/UnifiedMetricCard";
import { UnifiedPortfolioAssets } from "@/components/shared/UnifiedPortfolioAssets";
import { GeneratorPortfolioTable } from "@/components/generator/GeneratorPortfolioTable";
import { PortfolioAllocationAnalysis } from "@/components/shared/PortfolioAllocationAnalysis";
import { GeneratorChatBox } from "@/components/generator/GeneratorChatBox";
import ManualPortfolioService, { ManualPortfolioConfig } from "@/lib/services/ManualPortfolioService";
import { portfolioService } from "@/lib/services/PortfolioService";
import CoinService from "@/lib/services/CoinService";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';


import { PortfolioTemplateSelector } from "@/components/portfolio/PortfolioTemplateSelector";
import { EducationalTooltip, TermTooltip } from "@/components/shared/EducationalTooltip";
import { investmentEducation, metricsEducation } from "@/data/educationalContent";
import { PortfolioTemplate } from "@/data/portfolioTemplates";
import { PortfolioSnapshotDialog } from "@/components/portfolio/PortfolioSnapshotDialog";
import { PortfolioSnapshotWrapper } from "@/components/portfolio/PortfolioSnapshotWrapper";
import { SharedExportModal, ExportSection, FormatOption } from "@/components/shared/SharedExportModal";
import { jsPDF } from 'jspdf';
import { FileText, FileSpreadsheet } from "lucide-react";

interface Asset {
  symbol: string;
  fullName: string;
  percentage: number;
  value: number;
  color: string;
  score?: number;
  description?: string;
  id?: string;
  cryptoId?: string;
}

interface PortfolioStats {
  riskScore: { value: number; change: string };
  totalValue: { value: number; change: string };
  concentration: { value: number; change: string };
}

interface PortfolioMetrics {
  riskScore: { value: number; change: string };
  allocationBalance: { value: number; change: string };
  sharpeRatio: { value: number; change: string };
  diversification: { value: number; change: string };
  marketCapDistribution: { value: number; change: string };
}

interface SavedPortfolio {
  id: string;
  name: string;
  createdAt: string;
  lastModified: string;
  budget: string;
  customBudget: string;
  riskProfile: string;
  timeframe: string;
  selectedSectors: string[];
  portfolioStats: PortfolioStats;
  portfolioAllocation: Asset[];
}

interface ScoreMetric {
  name: string;
  value: number;
}

const performanceData = [
  { date: '2024-12', value: 5000, score: 76 },
  { date: '2025-01', value: 5250, score: 82 },
  { date: '2025-02', value: 5100, score: 79 },
  { date: '2025-03', value: 5400, score: 85 },
  { date: '2025-04', value: 5800, score: 88 },
  { date: '2025-05', value: 5600, score: 84 },
  { date: '2025-06', value: 6000, score: 89 }
].map(item => ({
  ...item,
  formattedValue: `$${(item.value).toLocaleString()}`,
  percentageChange: ((item.value - 5000) / 5000 * 100).toFixed(1),
  scoreChange: (item.score - 76).toFixed(1)
}));

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <StyledCard variant="primary" className="p-3">
        <p className="text-sm font-medium">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm">
            <span className="font-medium">{entry.name}: </span>
            {entry.name === 'Portfolio Value' ?
              `$${entry.value.toLocaleString()}` :
              `${entry.value}`}
          </p>
        ))}
      </StyledCard>
    );
  }
  return null;
};

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.4,
      staggerChildren: 0.1,
      when: "beforeChildren"
    }
  }
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
      duration: 0.4
    }
  }
};


const SectorDiversificationMetric = ({ sectorCount, score }: { sectorCount: number; score: number }) => (
  <StyledCard
    variant="primary"
    header={<span className="text-lg font-bold">Sector Diversification</span>}
  >
    <div>Sectors: {sectorCount}</div>
    <div>Score: {score}</div>
  </StyledCard>
);

// Using the shared NumberedHeader component from our design system



const PortfolioHealthScore = ({ scoreContributions, totalScore }: { scoreContributions: ScoreMetric[]; totalScore: number }) => (
  <StyledCard
    variant="primary"
    header={<span className="text-lg font-bold">Portfolio Health Score</span>}
  >
    <div>Score: {totalScore}</div>
    {/* Add score contributions here */}
  </StyledCard>
);


// Function to generate a deterministic color based on a string (like a symbol)
// Function to convert Tailwind color classes to actual color values
function getTailwindColor(colorClass: string): string {
  // Return actual hex color based on Tailwind class names
  const colorMap: Record<string, string> = {
    'bg-emerald-500': '#10b981', // emerald-500
    'bg-blue-500': '#3b82f6',    // blue-500
    'bg-violet-500': '#8b5cf6',  // violet-500
    'bg-red-500': '#ef4444',     // red-500
    'bg-amber-500': '#f59e0b',   // amber-500
    'bg-pink-500': '#ec4899',    // pink-500
    'bg-cyan-500': '#06b6d4',    // cyan-500
    'bg-indigo-500': '#6366f1',  // indigo-500
    'bg-yellow-500': '#eab308',  // yellow-500
    'bg-purple-500': '#a855f7',  // purple-500
    'bg-rose-500': '#f43f5e',    // rose-500
    'bg-teal-500': '#14b8a6',    // teal-500
    'bg-green-500': '#22c55e',   // green-500
    'bg-lime-500': '#84cc16',    // lime-500
    'bg-orange-500': '#f97316',  // orange-500
  };

  return colorMap[colorClass] || '#3b82f6'; // Default to blue if not found
}

function getRandomColor(str: string): string {
  // Simple hash function to generate a number from a string
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }

  // Convert to a color with good saturation and lightness for visibility
  const h = Math.abs(hash) % 360;
  return `hsl(${h}, 70%, 60%)`;
}

export default function Generator() {
  const { user } = useAuth();
  const { currentLanguage } = useLanguage();

  // Helper function to assign colors to assets
  const getAssetColor = (symbol: string): string => {
    const colorMap: Record<string, string> = {
      'BTC': 'bg-amber-500',
      'ETH': 'bg-indigo-500',
      'SOL': 'bg-purple-500',
      'BNB': 'bg-yellow-500',
      'LINK': 'bg-blue-500',
      'ADA': 'bg-blue-600',
      'DOT': 'bg-pink-500',
      'MATIC': 'bg-indigo-600',
      'ATOM': 'bg-purple-600',
      'RENDER': 'bg-green-500',
      'HNT': 'bg-teal-500',
      'PYTH': 'bg-orange-500',
      'HBAR': 'bg-gray-500',
      'RAY': 'bg-cyan-500',
      'VIRTUAL': 'bg-violet-500',
      'USDY': 'bg-emerald-500',
      'PENGU': 'bg-blue-400',
      'S': 'bg-red-500'
    };
    return colorMap[symbol] || 'bg-slate-500'; // Default color for unknown symbols
  };
  // Add loading state to fix first-load rendering issues
  const [isLoading, setIsLoading] = useState(true);
  const [budget, setBudget] = useState('medium');
  const [customBudget, setCustomBudget] = useState('');
  const [riskProfile, setRiskProfile] = useState<RiskProfileType>('balanced');
  const [selectedCurrency, setSelectedCurrency] = useState<string>('USD');
  const [timeframe, setTimeframe] = useState('medium');
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  // Advanced settings states
  const [minAllocation, setMinAllocation] = useState('2');
  const [maxAllocation, setMaxAllocation] = useState('30');
  const [includeStablecoins, setIncludeStablecoins] = useState(false);

  const [rebalancingFrequency, setRebalancingFrequency] = useState('monthly');
  const [liquidityPreference, setLiquidityPreference] = useState('medium');

  const [selectedSectors, setSelectedSectors] = useState<string[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState<'manual' | 'templates' | 'chat'>('templates');
  const chatBoxRef = useRef<HTMLDivElement>(null);
  const [portfolioStats, setPortfolioStats] = useState<PortfolioStats>({
    riskScore: { value: 5.0, change: '+2.5%' },
    totalValue: { value: 5000, change: '+2.5%' },
    concentration: { value: 8.0, change: '+2.1%' }
  });
  const [portfolioMetrics, setPortfolioMetrics] = useState<PortfolioMetrics>({
    riskScore: { value: 0, change: '0%' },
    allocationBalance: { value: 0, change: '0%' },
    sharpeRatio: { value: 0, change: '0%' },
    diversification: { value: 0, change: '0%' },
    marketCapDistribution: { value: 0, change: '0%' }
  });
  const [totalScore, setTotalScore] = useState<number>(0); // Separate state for total score
  const [portfolioName, setPortfolioName] = useState('');
  const [savedPortfolios, setSavedPortfolios] = useState<SavedPortfolio[]>([]);
  const [selectedPortfolioId, setSelectedPortfolioId] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [portfolioToDelete, setPortfolioToDelete] = useState<string | null>(null);

  // State for other confirmations
  const [showResetConfirm, setShowResetConfirm] = useState(false);
  const [showGenerateConfirm, setShowGenerateConfirm] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [editingPortfolioName, setEditingPortfolioName] = useState('');
  const [showRenameDialog, setShowRenameDialog] = useState(false);
  const [portfolioAllocation, setPortfolioAllocation] = useState<Asset[]>([
    // Major cryptocurrencies
    {
      symbol: 'BTC',
      fullName: 'Bitcoin',
      percentage: 18,
      value: 900,
      color: 'bg-amber-500',
      score: 92,
      description: 'The first and most well-known cryptocurrency, designed as a decentralized digital currency.'
    },
    {
      symbol: 'ETH',
      fullName: 'Ethereum',
      percentage: 15,
      value: 750,
      color: 'bg-indigo-500',
      score: 88,
      description: 'A decentralized platform that enables smart contracts and decentralized applications (DApps).'
    },
    // Layer 1 blockchains
    {
      symbol: 'SOL',
      fullName: 'Solana',
      percentage: 8,
      value: 400,
      color: 'bg-purple-500',
      score: 82,
      description: 'High-performance blockchain platform known for its fast transaction speeds and low costs.'
    },
    {
      symbol: 'ADA',
      fullName: 'Cardano',
      percentage: 5,
      value: 250,
      color: 'bg-blue-500',
      score: 75,
      description: 'Proof-of-stake blockchain platform with a focus on sustainability and scalability.'
    },
    {
      symbol: 'AVAX',
      fullName: 'Avalanche',
      percentage: 4,
      value: 200,
      color: 'bg-red-500',
      score: 78,
      description: 'Smart contract platform focusing on speed, low costs, and eco-friendliness.'
    },
    {
      symbol: 'DOT',
      fullName: 'Polkadot',
      percentage: 3,
      value: 150,
      color: 'bg-pink-500',
      score: 74,
      description: 'Protocol connecting blockchains, allowing value and data to be sent across networks.'
    },
    // DeFi tokens
    {
      symbol: 'UNI',
      fullName: 'Uniswap',
      percentage: 6,
      value: 300,
      color: 'bg-pink-400',
      score: 76,
      description: 'Decentralized trading protocol for automated liquidity provision on Ethereum.'
    },
    {
      symbol: 'AAVE',
      fullName: 'Aave',
      percentage: 4,
      value: 200,
      color: 'bg-purple-400',
      score: 73,
      description: 'Decentralized lending protocol allowing users to lend and borrow cryptocurrencies.'
    },
    {
      symbol: 'COMP',
      fullName: 'Compound',
      percentage: 2,
      value: 100,
      color: 'bg-green-500',
      score: 70,
      description: 'Algorithmic, autonomous interest rate protocol for lending and borrowing crypto.'
    },
    // AI/Gaming tokens
    {
      symbol: 'FET',
      fullName: 'Fetch.ai',
      percentage: 3,
      value: 150,
      color: 'bg-cyan-500',
      score: 68,
      description: 'AI and machine learning blockchain platform for autonomous economic agents.'
    },
    {
      symbol: 'RNDR',
      fullName: 'Render',
      percentage: 3,
      value: 150,
      color: 'bg-orange-500',
      score: 71,
      description: 'Distributed GPU rendering network built on blockchain technology.'
    },
    {
      symbol: 'AXS',
      fullName: 'Axie Infinity',
      percentage: 2,
      value: 100,
      color: 'bg-blue-400',
      score: 65,
      description: 'Blockchain-based trading and battling game with player-owned economy.'
    },
    // Meme coins
    {
      symbol: 'DOGE',
      fullName: 'Dogecoin',
      percentage: 3,
      value: 150,
      color: 'bg-yellow-500',
      score: 58,
      description: 'Peer-to-peer digital currency featuring the Shiba Inu dog from the "Doge" meme.'
    },
    {
      symbol: 'SHIB',
      fullName: 'Shiba Inu',
      percentage: 2,
      value: 100,
      color: 'bg-orange-400',
      score: 52,
      description: 'Decentralized meme token that evolved into a vibrant ecosystem.'
    },
    {
      symbol: 'PEPE',
      fullName: 'Pepe',
      percentage: 1,
      value: 50,
      color: 'bg-green-400',
      score: 45,
      description: 'Meme coin inspired by the popular Pepe the Frog internet meme.'
    },
    // Stablecoins
    {
      symbol: 'USDT',
      fullName: 'Tether',
      percentage: 5,
      value: 250,
      color: 'bg-emerald-500',
      score: 85,
      description: 'Stablecoin pegged to the US dollar, providing stability in crypto portfolios.'
    },
    {
      symbol: 'USDC',
      fullName: 'USD Coin',
      percentage: 5,
      value: 250,
      color: 'bg-blue-600',
      score: 87,
      description: 'Fully backed stablecoin pegged 1:1 to the US dollar.'
    },
    // Infrastructure tokens
    {
      symbol: 'LINK',
      fullName: 'Chainlink',
      percentage: 4,
      value: 200,
      color: 'bg-blue-700',
      score: 79,
      description: 'Decentralized oracle network providing real-world data to smart contracts.'
    },
    {
      symbol: 'FIL',
      fullName: 'Filecoin',
      percentage: 2,
      value: 100,
      color: 'bg-cyan-600',
      score: 69,
      description: 'Decentralized storage network designed to store humanity\'s most important information.'
    },
    {
      symbol: 'AR',
      fullName: 'Arweave',
      percentage: 2,
      value: 100,
      color: 'bg-gray-600',
      score: 72,
      description: 'Decentralized storage network for permanently hosting data with a single upfront fee.'
    }
  ]);

  // Create a portfolio object from API data only - no mock/fallback calculations
  const generatedPortfolio = React.useMemo(() => {
    if (!portfolioAllocation || portfolioAllocation.length === 0) {
      return null; // Return null if no data available
    }

    const currentDate = new Date();
    return {
      id: 'generated',
      name: portfolioName || 'Generated Portfolio',
      totalValue: 0, // Will be calculated from API data
      dayChange: 0, // API data only
      dayChangePercentage: 0, // API data only
      weekChangePercentage: 0, // API data only
      monthChangePercentage: 0, // API data only
      profit: 0, // API data only
      profitPercentage: 0, // API data only
      totalInvested: 0, // API data only
      createdAt: currentDate,
      updatedAt: currentDate,
      theme: 'default' as PortfolioTheme,
      assets: portfolioAllocation.map(asset => ({
        id: asset.symbol,
        name: asset.fullName,
        symbol: asset.symbol,
        allocation: asset.percentage,
        value: asset.value || 0, // API data only
        price: 0, // API data only - no fallback prices
        priceChangePercentage: 0, // API data only
        color: asset.color || 'bg-gray-500',
        tokenImageUrl: `/images/coins/${asset.symbol.toLowerCase()}.png`,
        score: asset.score ?? 0, // API data only
        amount: 0, // API data only
        change24h: 0, // API data only
        aiScore: asset.score ?? 0, // API data only
        logoUrl: `/images/coins/${asset.symbol.toLowerCase()}.png`,
        description: asset.description || `${asset.fullName} (${asset.symbol})`,
        sector: 'Other', // API data - sector categorization
        marketCap: 0, // API data only
        volume24h: 0 // API data only
      })),
      overallScore: totalScore, // Add overallScore field for PortfolioAllocationAnalysis
      metrics: {
        totalScore: totalScore, // Real API total score
        category_diversification: {
          score: portfolioMetrics.allocationBalance.value,
          contribution: portfolioMetrics.allocationBalance.value / 10,
          maxScore: 100
        },
        trend_category: {
          score: portfolioMetrics.sharpeRatio.value,
          contribution: portfolioMetrics.sharpeRatio.value / 10,
          maxScore: 100
        },
        marketCapDistribution: {
          score: portfolioMetrics.marketCapDistribution.value,
          contribution: portfolioMetrics.marketCapDistribution.value / 10,
          maxScore: 100
        },
        investmentBalance: {
          score: portfolioStats.concentration.value,
          contribution: portfolioStats.concentration.value / 10,
          maxScore: 100
        },
        stablecoinRatio: {
          score: portfolioMetrics.diversification.value,
          contribution: portfolioMetrics.diversification.value / 10,
          maxScore: 100
        },
        portfolio_quality: {
          score: portfolioMetrics.riskScore.value,
          contribution: portfolioMetrics.riskScore.value / 10,
          maxScore: 100
        },
        portfolio_weight: {
          score: portfolioStats.concentration.value,
          contribution: portfolioStats.concentration.value / 10,
          maxScore: 100
        }
      }
    };
  }, [portfolioAllocation, portfolioStats, portfolioMetrics, portfolioName, totalScore]);

  const [isSaving, setIsSaving] = useState(false);
  const [portfolioSuggestion, setPortfolioSuggestion] = useState<PortfolioSuggestion | null>(null);
  const [hoveredValue, setHoveredValue] = useState('');



  // State for portfolio templates
  const [showTemplates, setShowTemplates] = useState(true); // Show templates by default
  const [templateApplied, setTemplateApplied] = useState(false);
  const [appliedTemplateData, setAppliedTemplateData] = useState<PortfolioTemplate | null>(null);

  // Modal states for portfolio save
  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false);
  const [modalPortfolioName, setModalPortfolioName] = useState("");
  const [modalBudget, setModalBudget] = useState("");
  const [templateFilters, setTemplateFilters] = useState<{
    riskLevel?: 'low' | 'medium' | 'high';
    timeframe?: 'short' | 'medium' | 'long';
    search?: string;
    sortBy?: 'popularity' | 'newest' | 'name';
    sortOrder?: 'asc' | 'desc';
  }>({});

  // Query for fetching templates from API
  const {
    data: templatesResponse,
    isLoading: templatesLoading,
    error: templatesError,
    refetch: refetchTemplates
  } = useQuery({
    queryKey: ['portfolio-templates', templateFilters],
    queryFn: () => portfolioService.getPredefinedTemplates(templateFilters),
    enabled: activeTab === 'templates', // Only fetch when templates tab is active
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes (renamed from cacheTime in React Query v5)
  });

  // Extract templates from API response with fallback to empty array
  const apiTemplates = templatesResponse?.success && templatesResponse?.output?.templates
    ? templatesResponse.output.templates.map((template: any) => ({
        ...template,
        volatility: template.riskLevel === 'low' ? 'Low' : template.riskLevel === 'medium' ? 'Medium' : 'High',
        likeCount: 0,
        isLikedByUser: false,
        isRecommended: template.isRecommended || false,
        totalScore: template.totalScore || 0,
        metrics: template.metrics || {},
        allocation: template.allocation.map((asset: any) => ({
          ...asset,
          value: 0, // Will be calculated based on portfolio value
          color: getAssetColor(asset.symbol),
          description: asset.description || `${asset.fullName} (${asset.symbol})`
        }))
      }))
    : [];

  // Categories Query
  const {
    data: categoriesData = [],
    isLoading: categoriesLoading,
    error: categoriesError
  } = useQuery({
    queryKey: ['categories'],
    queryFn: () => CoinService.getCategories(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    refetchOnReconnect: false
  });

  // Query client for mutations
  const queryClient = useQueryClient();

  // Mutation for liking/unliking templates
  const likeMutation = useMutation({
    mutationFn: (templateId: string) => portfolioService.toggleTemplateLike(templateId),
    onSuccess: () => {
      // Refresh templates to get updated like counts
      queryClient.invalidateQueries({ queryKey: ['portfolio-templates'] });
    },
    onError: (error) => {
      console.error('Error toggling template like:', error);
    }
  });



  // State for portfolio snapshot dialog
  const [showSnapshotDialog, setShowSnapshotDialog] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);

  // State for save portfolio modal
  const [showSavePortfolioModal, setShowSavePortfolioModal] = useState(false);

  const [savePortfolioData, setSavePortfolioData] = useState({
    name: '',
    totalValue: ''
  });



  // Initialize component and set loading to false after mounting
  useEffect(() => {
    // Small delay to ensure component is fully mounted
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 200);

    // Load saved portfolios from localStorage
    const saved = localStorage.getItem('savedPortfolios');
    if (saved) {
      setSavedPortfolios(JSON.parse(saved));
    }

    return () => clearTimeout(timer);
  }, []);

  const scoreMetrics: ScoreMetric[] = [
    { name: "AI Confidence", value: 85 },
    { name: "Tokenomics", value: 78 },
    { name: "Security", value: 92 },
    { name: "Social", value: 88 },
    { name: "Market", value: 83 },
    { name: "Fundamentals", value: 87 }
  ];

  // Portfolio concentration and metrics calculation

  // Calculate concentration score (higher = more concentrated, lower = more diversified)
  const calculateConcentration = (assets: Asset[]) => {
    // Herfindahl-Hirschman Index (HHI) - measure of concentration
    // Sum of squared percentages (as decimals)
    const hhi = assets.reduce((sum, asset) => {
      const decimal = asset.percentage / 100;
      return sum + (decimal * decimal);
    }, 0);

    // Normalize to a 0-10 scale where lower is more diversified
    return parseFloat((hhi * 100).toFixed(1));
  };

  // Categories from API - extract names from API response
  const availableCategories = categoriesData.map(category => {
    // Handle both string and object formats
    if (typeof category === 'string') {
      return category;
    }
    // If it's an object with name or id property
    return category.name || category.id || String(category);
  });

  // Split categories into trending and additional (for UI organization)
  // Use API data as primary source, with fallback to hardcoded for structure
  const trendingSectors = availableCategories.slice(0, 8); // First 8 as trending
  const additionalSectors = availableCategories.slice(8); // Rest as additional

  const handleSectorToggle = (sector: string) => {
    setSelectedSectors(prev =>
      prev.includes(sector)
        ? prev.filter(s => s !== sector)
        : [...prev, sector]
    );
  };

  const handleResetAll = () => {
    setBudget('medium');
    setCustomBudget('');
    setRiskProfile('balanced');
    setTimeframe('medium');
    setSelectedSectors([]);
    setSelectedCurrency('USD');
    setMinAllocation('2');
    setMaxAllocation('30');
    setIncludeStablecoins(false);
    setRebalancingFrequency('monthly');
    setLiquidityPreference('medium');
    setShowAdvancedSettings(false);
  };

  const handleSavePreferences = () => {
    const preferences = {
      budget,
      customBudget,
      riskProfile,
      timeframe,
      sectors: selectedSectors,
      selectedCurrency,
      minAllocation,
      maxAllocation,
      includeStablecoins,
      rebalancingFrequency,
      liquidityPreference,
    };
    localStorage.setItem('portfolioPreferences', JSON.stringify(preferences));
  };

  const handleGeneratePortfolio = async () => {
    // Show confirmation if there's already a portfolio
    if (showResults && !showGenerateConfirm) {
      setShowGenerateConfirm(true);
      return;
    }
    setShowResults(false);
    setIsGenerating(true);

    try {
      // Check if we're in manual configuration mode
      if (activeTab === 'manual') {
        // Calculate investment budget - use custom budget if provided, otherwise use category default
        let investmentBudget: number;
        if (budget === 'custom' && customBudget) {
          investmentBudget = parseFloat(customBudget);
        } else {
          investmentBudget = ManualPortfolioService.getBudgetValueFromCategory(budget);
        }

        // Use ManualPortfolioService for manual configuration (5 fields)
        const config: ManualPortfolioConfig = {
          investmentBudget,
          budgetCategory: budget,
          riskProfile,
          investmentTimeframe: timeframe,
          selectedCategories: selectedSectors
        };

        // Validate configuration
        const validation = ManualPortfolioService.validateConfig(config);
        if (!validation.valid) {
          console.error('Manual Portfolio Validation Error:', validation.error);
          // Show error message to user
          alert(validation.error || 'Lütfen tüm gerekli alanları doldurun');
          setIsGenerating(false);
          return;
        }

        console.log('Manual portfolio config validated successfully:', config);

        // Call the manual portfolio generation API
        const response = await ManualPortfolioService.generatePortfolio(config);

        if (response.success && response.output) {
          const output = response.output;

          // Calculate total budget value for allocations
          const totalValue = investmentBudget;

          // Update portfolio allocation with API response
          const portfolioAssets = output.allocation.map((asset: any) => ({
            id: asset.id,
            symbol: asset.symbol,
            fullName: asset.fullName,
            percentage: asset.percentage,
            value: Math.round((asset.percentage / 100) * totalValue),
            color: getAssetColor(asset.symbol),
            score: asset.score ?? 0,
            description: asset.description || `${asset.fullName} (${asset.symbol})`
          }));

          setPortfolioAllocation(portfolioAssets);

          // Update portfolio metrics from API response
          if (output.metrics) {
            setPortfolioMetrics({
              riskScore: {
                value: output.metrics.portfolio_quality?.score ?? 0,
                change: `+${((output.metrics.portfolio_quality?.score ?? 0) / 10).toFixed(1)}%`
              },
              allocationBalance: {
                value: output.metrics.category_diversification?.score ?? 0,
                change: `+${((output.metrics.category_diversification?.score ?? 0) / 10).toFixed(1)}%`
              },
              sharpeRatio: {
                value: output.metrics.trend_category?.score ?? 0,
                change: `+${((output.metrics.trend_category?.score ?? 0) / 10).toFixed(1)}%`
              },
              diversification: {
                value: output.metrics.stablecoinRatio?.score ?? 0,
                change: `+${((output.metrics.stablecoinRatio?.score ?? 0) / 10).toFixed(1)}%`
              },
              marketCapDistribution: {
                value: output.metrics.marketCapDistribution?.score ?? 0,
                change: `+${((output.metrics.marketCapDistribution?.score ?? 0) / 10).toFixed(1)}%`
              }
            });
          }

          // Set total score from API
          setTotalScore(output.totalScore ?? 0);

          // Update portfolio stats from API
          setPortfolioStats({
            riskScore: {
              value: output.metrics?.portfolio_quality?.score ?? 0,
              change: `+${((output.metrics?.portfolio_quality?.score ?? 0) / 10).toFixed(1)}%`
            },
            totalValue: {
              value: totalValue,
              change: '+0.0%'
            },
            concentration: {
              value: output.metrics?.portfolio_weight?.score ?? 0,
              change: `+${((output.metrics?.portfolio_weight?.score ?? 0) / 10).toFixed(1)}%`
            }
          });

          // Set portfolio name from response
          setPortfolioName(output.name || 'AI Generated Portfolio');

          // Store template data for later use
          setAppliedTemplateData({
            ...output,
            isAiGenerated: true
          } as any);

          setIsGenerating(false);

          // Show results immediately (like AI Assistant does)
          setShowResults(true);
          setTemplateApplied(true);

          // Smooth scroll to results
          setTimeout(() => {
            const resultsSection = document.getElementById('portfolio-results');
            if (resultsSection) {
              resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
          }, 300);

          return;
        }
      }

      // Fallback to existing logic for templates and chat or if manual service fails
      // Validate all required parameters
      const missingParams = [];

      // Check budget
      if (!budget && (!customBudget || parseFloat(customBudget) <= 0)) {
        missingParams.push("Investment Budget");
      }

      // Check risk profile
      if (!riskProfile) {
        missingParams.push("Risk Profile");
      }

      // Check timeframe
      if (!timeframe) {
        missingParams.push("Investment Timeframe");
      }

      // Trending categories are now optional - no validation needed

      // If any parameters are missing, show error
      if (missingParams.length > 0) {
        setIsGenerating(false);
        return;
      }

      // Simulate portfolio generation delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

    const suggestion = getPortfolioSuggestion(riskProfile, selectedSectors);
    const analysis = analyzePortfolio(
      suggestion.assets.reduce((acc: Record<string, number>, asset) => ({
        ...acc,
        [asset.cryptoId]: asset.weight
      }), {}),
      riskProfile
    );

    setPortfolioSuggestion(suggestion);

    // Update portfolio metrics based on the suggestion
    setPortfolioMetrics({
      riskScore: {
        value: (analysis?.riskMetrics?.totalVolatility || 0) * 10,
        change: '+2.1%'
      },
      allocationBalance: {
        value: (analysis?.diversificationScore || 0) * 10,
        change: '+3.5%'
      },
      sharpeRatio: {
        value: analysis?.riskMetrics?.sharpeRatio || 0,
        change: '+1.2%'
      },
      diversification: {
        value: (analysis?.diversificationScore || 0) * 10,
        change: '+1.8%'
      },
      marketCapDistribution: {
        value: (analysis?.diversificationScore || 0) * 10,
        change: '+2.0%'
      }
    });

    // Update portfolio stats
    setPortfolioStats({
      riskScore: {
        value: (analysis?.riskMetrics?.totalVolatility || 0) * 10,
        change: '+2.5%'
      },
      totalValue: {
        value: 5000, // Default to $5000 for the example
        change: '+2.5%'
      },
      concentration: {
        value: (1 - analysis?.diversificationScore || 0) * 10,
        change: '+2.1%'
      }
    });

    // Update portfolio allocation based on the suggestion
    const totalValue = 5000; // Default to $5000 for the example
    const newAllocation = suggestion.assets.map(asset => {
      const value = asset.weight * totalValue;
      const percentage = asset.weight * 100;

      // Define color based on risk level
      let color = 'bg-blue-500'; // default
      if (asset.riskLevel === 'low') {
        color = 'bg-green-500';
      } else if (asset.riskLevel === 'medium') {
        color = 'bg-amber-500';
      } else if (asset.riskLevel === 'high') {
        color = 'bg-rose-500';
      }

      // Convert cryptoId to a proper symbol and full name
      const symbolMap: Record<string, {symbol: string, fullName: string}> = {
        'bitcoin': { symbol: 'BTC', fullName: 'Bitcoin' },
        'ethereum': { symbol: 'ETH', fullName: 'Ethereum' },
        'solana': { symbol: 'SOL', fullName: 'Solana' },
        'cardano': { symbol: 'ADA', fullName: 'Cardano' },
        'polkadot': { symbol: 'DOT', fullName: 'Polkadot' },
        'avalanche': { symbol: 'AVAX', fullName: 'Avalanche' },
        'aptos': { symbol: 'APT', fullName: 'Aptos' },
        'sui': { symbol: 'SUI', fullName: 'Sui' },
        'binance': { symbol: 'BNB', fullName: 'Binance Coin' }
      };

      const assetInfo = symbolMap[asset.cryptoId] || {
        symbol: asset.cryptoId.slice(0, 3).toUpperCase(),
        fullName: asset.cryptoId.charAt(0).toUpperCase() + asset.cryptoId.slice(1)
      };

      return {
        id: asset.cryptoId, // Include the original cryptoId as id
        symbol: assetInfo.symbol,
        fullName: assetInfo.fullName,
        percentage,
        value,
        color,
        score: asset.coinScoutScore,
        description: asset.reason
      };
    });

    setPortfolioAllocation(newAllocation);
    setIsGenerating(false);
    setShowResults(true);

    // Generate automatic portfolio name if not already set
    if (!portfolioName || !selectedPortfolioId) {
      const date = new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      const riskLabel = riskProfile.charAt(0).toUpperCase() + riskProfile.slice(1);
      const categoryLabel = selectedSectors.length > 0 ? selectedSectors[0] : 'Mixed';
      const generatedName = `${riskLabel} ${categoryLabel} Portfolio - ${date}`;
      setPortfolioName(generatedName);
    }

    // Smooth scroll to results after a short delay
    setTimeout(() => {
      const resultsSection = document.getElementById('portfolio-results');
      if (resultsSection) {
        resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 300);

    } catch (error) {
      console.error('Error generating portfolio:', error);
      setIsGenerating(false);
      // You could add toast notification here for user feedback
    }
  };

  const handleSavePortfolio = async () => {
    if (!showResults) {
      console.error('❌ [Save Portfolio] No results available');
      return;
    }

    console.log('🔥 [Save Portfolio] Opening modal for portfolio save...');
    console.log('🔍 [Save Portfolio] Applied template data:', appliedTemplateData);
    console.log('🔍 [Save Portfolio] Template applied flag:', templateApplied);

    // Set initial values for modal
    setModalPortfolioName(portfolioName || 'My Portfolio');

    const getBudgetValue = () => {
      if (budget === 'beginner') return 500;
      if (budget === 'medium') return 5000;
      if (budget === 'advanced') return 25000;
      if (budget === 'whale') return 100000;
      return parseFloat(customBudget) || 5000;
    };

    setModalBudget(getBudgetValue().toString());

    // Open the modal for user input
    setIsSaveModalOpen(true);
  };

  // Handle actual portfolio save from modal
  const handleModalSave = async () => {
    if (!modalPortfolioName.trim() || !modalBudget.trim()) {
      console.error('❌ [Modal Save] Missing required data');
      return;
    }

    console.log('🚀 [Modal Save] Starting portfolio creation from modal...');
    console.log('🔍 [Modal Save] Portfolio Name:', modalPortfolioName);
    console.log('🔍 [Modal Save] Budget:', modalBudget);
    console.log('🔍 [Modal Save] Applied template data:', appliedTemplateData);
    console.log('🔍 [Modal Save] Template applied flag:', templateApplied);

    setIsSaving(true);

    try {
      let response;

      // Check if this is a predefined template (has appliedTemplateData with id)
      if (appliedTemplateData && appliedTemplateData.id) {
        console.log('🔥 [Modal Save] Using predefined template API...');
        response = await portfolioService.createPortfolioFromPredefinedTemplate({
          templateId: appliedTemplateData.id,
          portfolioName: modalPortfolioName,
          totalBudget: parseFloat(modalBudget)
        });
      } else {
        // This is an AI-generated template or manual portfolio
        console.log('🔥 [Modal Save] Using template/manual portfolio API...');
        console.log('🔥 [Modal Save] Portfolio allocation:', portfolioAllocation.map(a => ({ symbol: a.symbol, id: a.id, cryptoId: a.cryptoId })));

        // Prepare assets data with coin IDs and allocations
        const assetsData = portfolioAllocation.map(asset => {
          const coinId = asset.symbol; // Use symbol for API conversion
          console.log(`🔥 [Modal Save] Asset ${asset.symbol} → coinId: ${coinId}`);
          console.log(`🔥 [Modal Save] Asset full data:`, { symbol: asset.symbol, id: asset.id, percentage: asset.percentage });

          return {
            coinId: coinId, // Will be converted to numeric ID in PortfolioService
            allocation: asset.percentage
          };
        });

        console.log('🔥 [Modal Save] Assets data prepared for API:', assetsData);
        console.log('🔥 [Modal Save] About to call createPortfolioFromTemplate API...');

        response = await portfolioService.createPortfolioFromTemplate({
          name: modalPortfolioName,
          budget: parseFloat(modalBudget),
          totalValue: parseFloat(modalBudget),
          assets: assetsData
        });

        console.log('🔥 [Modal Save] createPortfolioFromTemplate API response:', response);
      }

      if (response.success) {
        console.log('✅ [Modal Save] Portfolio created successfully:', response);

        // Invalidate portfolios cache to refresh the list in Portfolio Analysis page
        queryClient.invalidateQueries({ queryKey: ['portfolios'] });
        console.log('🔄 [Modal Save] Invalidated portfolios cache for refresh');

        // Also save to localStorage for backward compatibility
        const portfolioToSave: SavedPortfolio = {
          id: response.data?.portfolioId || crypto.randomUUID(),
          name: modalPortfolioName,
          createdAt: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          budget,
          customBudget: modalBudget,
          riskProfile,
          timeframe,
          selectedSectors,
          portfolioStats,
          portfolioAllocation,
        };

        let updatedPortfolios = [...savedPortfolios];
        updatedPortfolios.push(portfolioToSave);
        localStorage.setItem('savedPortfolios', JSON.stringify(updatedPortfolios));
        setSavedPortfolios(updatedPortfolios);
        setSelectedPortfolioId(portfolioToSave.id);

        // Close modal and show success
        setIsSaveModalOpen(false);
        // You can add success toast or redirect here
      } else {
        console.error('❌ [Modal Save] Failed to create portfolio:', response.errormsg);
      }

    } catch (error) {
      console.error('❌ [Modal Save] Error creating portfolio:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSavePortfolioConfirm = async () => {
    if (!savePortfolioData.name.trim() || !savePortfolioData.totalValue.trim()) {
      return;
    }

    setIsSaving(true);

    try {
      console.log('🔥 [Save Portfolio Confirm] Starting portfolio save...');
      console.log('🔥 [Save Portfolio Confirm] Portfolio allocation:', portfolioAllocation.map(a => ({ symbol: a.symbol, id: a.id, cryptoId: a.cryptoId })));

      // Prepare assets data with coin IDs and allocations
      // Note: PortfolioService will convert symbols to numeric IDs automatically
      const assetsData = portfolioAllocation.map(asset => {
        const coinId = asset.id || asset.cryptoId || asset.symbol;
        console.log(`🔥 [Save Portfolio Confirm] Asset ${asset.symbol} → coinId: ${coinId}`);

        return {
          coinId: coinId, // Will be converted to numeric ID in PortfolioService
          allocation: asset.percentage
        };
      });

      console.log('🔥 [Save Portfolio Confirm] Assets data prepared for API:', assetsData);

      // Call portfolio service to create portfolio from template
      const response = await portfolioService.createPortfolioFromTemplate({
        name: savePortfolioData.name,
        budget: parseFloat(savePortfolioData.totalValue),
        totalValue: parseFloat(savePortfolioData.totalValue),
        assets: assetsData
      });

      if (response.success) {
        console.log('✅ Portfolio created successfully:', response);

        // Also save to localStorage for backward compatibility
        const portfolioToSave: SavedPortfolio = {
          id: response.data?.portfolioId || crypto.randomUUID(),
          name: savePortfolioData.name,
          createdAt: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          budget,
          customBudget: savePortfolioData.totalValue,
          riskProfile,
          timeframe,
          selectedSectors,
          portfolioStats,
          portfolioAllocation,
        };

        let updatedPortfolios = [...savedPortfolios];
        updatedPortfolios.push(portfolioToSave);
        localStorage.setItem('savedPortfolios', JSON.stringify(updatedPortfolios));
        setSavedPortfolios(updatedPortfolios);
        setSelectedPortfolioId(portfolioToSave.id);

        // Close modal
        setShowSavePortfolioModal(false);
      } else {
        console.error('❌ Portfolio creation failed:', response.errormsg);
      }

    } catch (error) {
      console.error('❌ Error creating portfolio:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleLoadPortfolio = (portfolioId: string) => {
    const portfolio = savedPortfolios.find(p => p.id === portfolioId);
    if (!portfolio) return;

    // Load portfolio data
    setSelectedPortfolioId(portfolioId);
    setPortfolioName(portfolio.name);
    setBudget(portfolio.budget);
    setCustomBudget(portfolio.customBudget);
    setRiskProfile(portfolio.riskProfile as RiskProfileType);
    setTimeframe(portfolio.timeframe);
    setSelectedSectors(portfolio.selectedSectors);
    setPortfolioStats(portfolio.portfolioStats);
    setPortfolioAllocation(portfolio.portfolioAllocation);
    setShowResults(true);

  };

  const handleDeletePortfolio = () => {
    if (!portfolioToDelete) return;

    const updatedPortfolios = savedPortfolios.filter(p => p.id !== portfolioToDelete);
    localStorage.setItem('savedPortfolios', JSON.stringify(updatedPortfolios));
    setSavedPortfolios(updatedPortfolios);

    // Clear selection if the deleted portfolio was selected
    if (selectedPortfolioId === portfolioToDelete) {
      setSelectedPortfolioId(null);
      setPortfolioName('');
    }


    setShowDeleteConfirm(false);
    setPortfolioToDelete(null);
  };

  const handleRenamePortfolio = () => {
    if (!selectedPortfolioId || !editingPortfolioName.trim()) return;

    const updatedPortfolios = savedPortfolios.map(p =>
      p.id === selectedPortfolioId
        ? { ...p, name: editingPortfolioName, lastModified: new Date().toISOString() }
        : p
    );

    localStorage.setItem('savedPortfolios', JSON.stringify(updatedPortfolios));
    setSavedPortfolios(updatedPortfolios);
    setPortfolioName(editingPortfolioName);
    setShowRenameDialog(false);
    setEditingPortfolioName('');

  };

  const handleMigrateToAnalysis = () => {
    if (!showResults) {
      return;
    }

    // In a real implementation, this would pass the portfolio data to the portfolio analysis page
    window.location.href = '/portfolio';

  };



  const handleSharePortfolio = () => {
    if (!showResults) {
      return;
    }

    // Show the portfolio snapshot dialog
    setShowSnapshotDialog(true);
  };

  const handleExportPDF = () => {
    setShowExportModal(true);
  };

  // Export modal configuration
  const exportSections: ExportSection[] = [
    {
      id: 'summary',
      label: 'Portfolio Overview',
      description: 'Budget, risk profile, timeframe, and key metrics',
      defaultSelected: true,
      availableIn: ['pdf', 'excel']
    },
    {
      id: 'allocation',
      label: 'Asset Allocation',
      description: 'Complete breakdown of cryptocurrencies and percentages',
      defaultSelected: true,
      availableIn: ['pdf', 'excel']
    },
    {
      id: 'analysis',
      label: 'Portfolio Analysis',
      description: 'Health scores, diversification metrics, and AI insights',
      defaultSelected: true,
      availableIn: ['pdf', 'excel']
    },
    {
      id: 'charts',
      label: 'Visual Charts',
      description: 'Allocation pie chart and category breakdown',
      defaultSelected: true,
      availableIn: ['pdf']
    },
    {
      id: 'recommendations',
      label: 'AI Recommendations',
      description: 'Generated insights and portfolio optimization tips',
      defaultSelected: false,
      availableIn: ['pdf', 'excel']
    }
  ];

  const exportFormatOptions: FormatOption[] = [
    {
      id: 'pdf',
      label: 'PDF Report',
      description: 'Professional report for sharing',
      icon: FileText,
      recommended: true
    },
    {
      id: 'excel',
      label: 'Excel',
      description: 'For further analysis & tracking',
      icon: FileSpreadsheet,
      recommended: false
    }
  ];

  const handleExportAction = async (format: string, selectedSections: string[]) => {
    if (format === 'pdf') {
      // Generate PDF
      const pdf = new jsPDF();
      const pageWidth = pdf.internal.pageSize.getWidth();
      const margin = 20;
      let yPosition = margin;

      // Add title
      pdf.setFontSize(24);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${portfolioName} - AI Generated Portfolio`, margin, yPosition);
      yPosition += 15;

      // Add date
      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`Generated on ${new Date().toLocaleDateString()}`, margin, yPosition);
      yPosition += 20;

      // Portfolio Overview
      if (selectedSections.includes('summary')) {
        pdf.setFontSize(16);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Portfolio Overview', margin, yPosition);
        yPosition += 10;

        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'normal');

        const getBudgetValue = () => {
          if (budget === 'beginner') return '$100 - $1,000';
          if (budget === 'medium') return '$1,000 - $10,000';
          if (budget === 'advanced') return '$10,000 - $100,000';
          if (budget === 'whale') return '$100,000+';
          return `$${customBudget}`;
        };

        const summaryData = [
          [`Investment Budget: ${getBudgetValue()}`],
          [`Risk Profile: ${riskProfile.charAt(0).toUpperCase() + riskProfile.slice(1)}`],
          [`Investment Timeframe: ${timeframe === 'short' ? 'Short-term (< 1 year)' : timeframe === 'medium' ? 'Medium-term (1-3 years)' : 'Long-term (3+ years)'}`],
          [`Total Score: 89/100`], // Default score shown in UI
          [`Categories: ${selectedSectors.join(', ')}`]
        ];

        summaryData.forEach((item) => {
          pdf.text(item[0], margin, yPosition);
          yPosition += 7;
        });

        yPosition += 10;
      }

      // Asset Allocation
      if (selectedSections.includes('allocation')) {
        pdf.setFontSize(16);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Asset Allocation', margin, yPosition);
        yPosition += 10;

        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'normal');

        portfolioAllocation.forEach((asset: any) => {
          if (yPosition > 270) {
            pdf.addPage();
            yPosition = margin;
          }

          pdf.text(`${asset.symbol} (${asset.name}) - ${asset.percentage.toFixed(1)}% - $${asset.value.toLocaleString()}`, margin, yPosition);
          yPosition += 6;
        });

        yPosition += 10;
      }

      // Download the PDF
      pdf.save(`${portfolioName.replace(/\s+/g, '_')}_portfolio_${new Date().toISOString().split('T')[0]}.pdf`);
    } else {
      // Excel export logic would go here
      console.log('Excel export not yet implemented');
    }
  };

  const handleCreateNewPortfolio = () => {
    // Reset all states to start fresh
    setShowResults(false);
    setShowTemplates(false);
    setPortfolioName('');
    setSelectedPortfolioId(null);
    setBudget('medium');
    setCustomBudget('');
    setRiskProfile('balanced');
    setTimeframe('medium');
    setSelectedSectors([]);
    setIsGenerating(false);

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });

  };




  // Handle applying a portfolio template
  const handleApplyTemplate = (template: PortfolioTemplate) => {
    console.log('🚀 [handleApplyTemplate] Applying template directly:', template.name);

    // Get the total budget value
    const getBudgetValue = () => {
      if (budget === 'beginner') return 500;
      if (budget === 'medium') return 5000;
      if (budget === 'advanced') return 25000;
      if (budget === 'whale') return 100000;
      return parseFloat(customBudget) || 5000;
    };

    const totalBudget = getBudgetValue();

    // Calculate asset values based on percentages and total budget
    const calculatedAssets = template.allocation.map(asset => ({
      ...asset,
      value: (asset.percentage / 100) * totalBudget
    }));

    // Apply template directly without API call
    setPortfolioAllocation(calculatedAssets);
    setPortfolioName(`${template.name} Portfolio`);
    setRiskProfile(template.riskLevel === 'low' ? 'low' :
                  template.riskLevel === 'medium' ? 'balanced' : 'high');
    setTimeframe(template.timeframe);

    // Apply template metrics if available
    if (template.metrics) {
      setPortfolioMetrics({
        riskScore: {
          value: template.metrics.portfolio_quality?.score ?? 0,
          change: `+${((template.metrics.portfolio_quality?.score ?? 0) / 10).toFixed(1)}%`
        },
        allocationBalance: {
          value: template.metrics.category_diversification?.score ?? 0,
          change: `+${((template.metrics.category_diversification?.score ?? 0) / 10).toFixed(1)}%`
        },
        sharpeRatio: {
          value: template.metrics.trend_category?.score ?? 0,
          change: `+${((template.metrics.trend_category?.score ?? 0) / 10).toFixed(1)}%`
        },
        diversification: {
          value: template.metrics.stablecoinRatio?.score ?? 0,
          change: `+${((template.metrics.stablecoinRatio?.score ?? 0) / 10).toFixed(1)}%`
        },
        marketCapDistribution: {
          value: template.metrics.marketCapDistribution?.score ?? 0,
          change: `+${((template.metrics.marketCapDistribution?.score ?? 0) / 10).toFixed(1)}%`
        }
      });

      setPortfolioStats({
        riskScore: {
          value: template.metrics.portfolio_quality?.score ?? 0,
          change: `+${((template.metrics.portfolio_quality?.score ?? 0) / 10).toFixed(1)}%`
        },
        totalValue: {
          value: totalBudget,
          change: '+0.0%'
        },
        concentration: {
          value: template.metrics.portfolio_weight?.score ?? 0,
          change: `+${((template.metrics.portfolio_weight?.score ?? 0) / 10).toFixed(1)}%`
        }
      });
    }

    // Set total score from template
    setTotalScore(template.totalScore ?? 0);

    // Store applied template data for later API calls
    setAppliedTemplateData({
      ...template,
      isAiGenerated: false // This is a predefined template
    } as any);

    // Extract sectors from template
    const sectorsFromTemplate = ['DeFi', 'AI', 'Layer 1', 'Layer 2', 'RWA'].filter(sector =>
      template.description?.toLowerCase().includes(sector.toLowerCase()) ||
      template.allocation?.some(asset => asset.description?.toLowerCase().includes(sector.toLowerCase()))
    );

    if (sectorsFromTemplate.length > 0) {
      setSelectedSectors(sectorsFromTemplate);
    }

    setTemplateApplied(true);
    setShowResults(true);
    setShowTemplates(false);

    console.log('✅ [handleApplyTemplate] Predefined template applied successfully!', {
      templateId: template.id,
      templateName: template.name,
      isAiGenerated: false
    });
  };

  const handleChatTemplateGenerated = (templateData: any) => {
    try {
      if (!templateData) {
        console.error('Invalid template data:', templateData);
        return;
      }

      const template = templateData;

      // Apply template settings
      setRiskProfile(template.riskLevel === 'low' ? 'low' :
                    template.riskLevel === 'medium' ? 'balanced' : 'high');
      setTimeframe(template.timeframe);

      // Use API data for portfolio assets
      const totalValue = template.allocation.reduce((sum: number, asset: any) => sum + (asset.value || 0), 0) || 5000;
      const portfolioAssets = template.allocation.map((asset: any) => ({
        id: asset.id, // Include the numeric ID from templateData
        symbol: asset.symbol,
        fullName: asset.fullName,
        percentage: asset.percentage,
        value: asset.value || Math.round((asset.percentage / 100) * totalValue),
        color: asset.color || 'bg-gray-500',
        score: asset.score ?? 0,
        description: asset.description || `${asset.fullName} (${asset.symbol})`
      }));

      // Update portfolio allocation
      setPortfolioAllocation(portfolioAssets);

      // Use real metrics from API response
      if (template.metrics) {
        setPortfolioMetrics({
          riskScore: {
            value: template.metrics.portfolio_quality?.score ?? 0,
            change: `+${((template.metrics.portfolio_quality?.score ?? 0) / 10).toFixed(1)}%`
          },
          allocationBalance: {
            value: template.metrics.category_diversification?.score ?? 0,
            change: `+${((template.metrics.category_diversification?.score ?? 0) / 10).toFixed(1)}%`
          },
          sharpeRatio: {
            value: template.metrics.trend_category?.score ?? 0,
            change: `+${((template.metrics.trend_category?.score ?? 0) / 10).toFixed(1)}%`
          },
          diversification: {
            value: template.metrics.stablecoinRatio?.score ?? 0,
            change: `+${((template.metrics.stablecoinRatio?.score ?? 0) / 10).toFixed(1)}%`
          },
          marketCapDistribution: {
            value: template.metrics.marketCapDistribution?.score ?? 0,
            change: `+${((template.metrics.marketCapDistribution?.score ?? 0) / 10).toFixed(1)}%`
          }
        });
      }

      // Set total score from API
      setTotalScore(template.totalScore ?? 0);

      // Use real portfolio stats from API
      setPortfolioStats({
        riskScore: {
          value: template.metrics?.portfolio_quality?.score ?? 0,
          change: `+${((template.metrics?.portfolio_quality?.score ?? 0) / 10).toFixed(1)}%`
        },
        totalValue: {
          value: totalValue,
          change: '+0.0%'
        },
        concentration: {
          value: template.metrics?.portfolio_weight?.score ?? 0,
          change: `+${((template.metrics?.portfolio_weight?.score ?? 0) / 10).toFixed(1)}%`
        }
      });

      // Set portfolio name based on template
      setPortfolioName(template.name || 'AI Generated Portfolio');

      // Show results only if we have valid data
      if (portfolioAssets && portfolioAssets.length > 0) {
        setShowResults(true);
        setTemplateApplied(true);

        // Store the template data for potential API calls (but without predefined template ID)
        // This will help distinguish between AI-generated and predefined templates
        setAppliedTemplateData({
          ...template,
          id: null, // AI-generated templates don't have predefined IDs
          isAiGenerated: true
        });
      }

      console.log('Chat template applied with real metrics:', {
        totalScore: template.totalScore,
        metrics: template.metrics,
        isAiGenerated: true
      });
    } catch (error) {
      console.error('Error applying chat template:', error);
    }
  };



  // Show loading indicator when the component is initializing
  if (isLoading) {
    return (
      <div className="flex flex-col h-screen justify-center items-center bg-slate-900/95">
        <div className="flex flex-col items-center gap-6">
          <Loader2 className="h-12 w-12 text-primary animate-spin" />
          <h2 className="text-xl font-medium text-primary">Loading Portfolio Generator</h2>
          <p className="text-slate-400 text-sm">Please wait while we prepare the AI generator...</p>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>

      <div className="min-h-screen bg-slate-900/95 text-slate-200">
        <div className="p-4 md:p-6 space-y-6 max-w-[1920px] mx-auto">
          {/* Page Title and Description - Outside Container */}
          <div className="mb-8">
            <div className="relative pb-6 border-b border-border/30">
              <div className="flex items-center gap-3 mb-3">
                <div className="h-8 w-1.5 bg-primary rounded-full"></div>
                <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-primary">
                  AI Portfolio Generator
                </h1>
              </div>
              <div className="flex items-center gap-2 ml-4">
                <Sparkles className="h-4 w-4 text-primary opacity-80" />
                <span className="text-sm text-muted-foreground font-medium">
                  Create your educational crypto portfolio in seconds - Choose a template or customize your own
                </span>
              </div>
            </div>
          </div>

          <StyledCard variant="primary" className="p-4 md:p-6 space-y-6 backdrop-blur-md">



            {/* Welcome Section for First-Time Users */}
            {!showResults && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 bg-blue-500/10 border border-blue-500/30 rounded-lg p-6 shadow-sm shadow-slate-900/20"
              >
                <div className="flex items-start gap-4">
                  <div className="h-12 w-12 rounded-full bg-blue-500/20 flex items-center justify-center flex-shrink-0">
                    <Sparkles className="h-6 w-6 text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <h2 className="text-lg font-semibold text-white mb-2">Welcome! Let's Create Your First Portfolio</h2>
                    <p className="text-sm text-slate-300 mb-4">
                      You can start quickly with our pre-configured templates or customize your own portfolio.
                      We've already selected smart defaults to help you get started.
                    </p>
                    <div className="flex flex-col gap-4">
                      {/* Tab Navigation */}
                      <div className="flex flex-wrap gap-2 border-b border-slate-600 pb-2">
                        <button
                          onClick={() => setActiveTab('templates')}
                          className={cn(
                            "px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2",
                            activeTab === 'templates'
                              ? "bg-blue-500 text-white border border-blue-500 shadow-lg"
                              : "bg-slate-700/50 text-slate-300 border border-slate-600 hover:bg-blue-500/20 hover:text-blue-400 hover:border-blue-500/50"
                          )}
                          data-testid="tab-templates"
                        >
                          <FolderOpen className="w-4 h-4" />
                          Choose from Templates
                        </button>
                        <button
                          onClick={() => setActiveTab('chat')}
                          className={cn(
                            "px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2",
                            activeTab === 'chat'
                              ? "bg-purple-500 text-white border border-purple-500 shadow-lg"
                              : "bg-slate-700/50 text-slate-300 border border-slate-600 hover:bg-purple-500/20 hover:text-purple-400 hover:border-purple-500/50"
                          )}
                          data-testid="tab-chat"
                        >
                          <Brain className="w-4 h-4" />
                          Build with AI Assistant
                        </button>
                        <button
                          onClick={() => setActiveTab('manual')}
                          className={cn(
                            "px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2",
                            activeTab === 'manual'
                              ? "bg-green-500 text-white border border-green-500 shadow-lg"
                              : "bg-slate-700/50 text-slate-300 border border-slate-600 hover:bg-green-500/20 hover:text-green-400 hover:border-green-500/50"
                          )}
                          data-testid="tab-manual"
                        >
                          <Settings className="w-4 h-4" />
                          Build Manually
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Step 1: Portfolio Templates Section */}
            {activeTab === 'templates' && !showResults && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ type: "spring", stiffness: 300, damping: 30, duration: 0.4 }}
                className="mb-8"
              >
                <StyledCard variant="primary" className="p-4">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
                      <span className="text-sm font-bold text-primary">1</span>
                    </div>
                    <h3 className="text-lg font-semibold text-white">Choose a Template</h3>
                    <EducationalTooltip
                      title="Portfolio Templates"
                      description="Templates provide pre-defined portfolios based on different investment strategies. They serve as starting points that you can customize to fit your specific needs."
                      tips={[
                        "Templates help you understand different investment approaches",
                        "Use them as educational tools to learn about asset allocation",
                        "Customize templates to better match your personal preferences"
                      ]}
                      type="tip"
                      side="top"
                      iconSize="sm"
                    />
                  </div>
                  {templatesLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                      <span className="ml-2 text-slate-400">Loading templates...</span>
                    </div>
                  ) : templatesError ? (
                    <div className="text-center py-8">
                      <div className="text-red-400 mb-2">Error loading templates</div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => refetchTemplates()}
                        className="text-slate-300"
                      >
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Retry
                      </Button>
                    </div>
                  ) : (
                    <PortfolioTemplateSelector
                      templates={apiTemplates}
                      onSelectTemplate={handleApplyTemplate}
                      onToggleLike={(templateId) => likeMutation.mutate(templateId)}
                      isApplying={false}
                      className="mb-2"
                    />
                  )}
                </StyledCard>
              </motion.div>
            )}

            {activeTab === 'chat' && !showResults && (
              <motion.div
                ref={chatBoxRef}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ type: "spring", stiffness: 300, damping: 30, duration: 0.4 }}
                className="mb-8"
              >
                <StyledCard variant="primary" className="p-4">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center">
                      <Brain className="w-4 h-4 text-purple-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-white">Chat with AI Assistant</h3>
                    <EducationalTooltip
                      title="AI Chat Portfolio Generation"
                      description="Describe your investment preferences in natural language and let our AI create a personalized portfolio for you."
                      tips={[
                        "Describe your risk tolerance, investment goals, and interests",
                        "Mention specific sectors or cryptocurrencies you prefer",
                        "Tell the AI about your time horizon and investment strategy"
                      ]}
                      type="tip"
                      side="top"
                      iconSize="sm"
                    />
                  </div>
                  <GeneratorChatBox onTemplateGenerated={handleChatTemplateGenerated} />
                </StyledCard>
              </motion.div>
            )}

            {/* Show Templates Button - only visible when templates are hidden */}


            {/* Template Selected Indicator - Shows when templates are active and results are not shown */}
            {activeTab === 'templates' && !showResults && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
                className="mb-6 bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 flex items-center gap-3 shadow-sm shadow-slate-900/20"
              >
                <CheckCircle className="h-5 w-5 text-blue-500" />
                <p className="text-sm text-slate-300">
                  Template mode active. The AI will configure all settings for you.
                </p>
              </motion.div>
            )}

            {/* Configuration Section - Shows when manual tab is active and results are not shown */}
            {activeTab === 'manual' && !showResults && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                variants={containerVariants}
                className="relative mb-8"
                id="step2-section"
              >
              <div className="p-6 border-b border-slate-700/40 bg-slate-800/50 backdrop-blur-md flex flex-col sm:flex-row sm:items-center justify-between gap-4 rounded-t-lg">
                <div className="flex items-center gap-3 flex-1">
                  <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
                    <span className="text-sm font-bold text-primary">2</span>
                  </div>
                  <div className="flex-1">
                    <h2 className="text-xl font-bold text-white">Customize Your Portfolio</h2>
                    <p className="text-slate-400 text-sm leading-relaxed">
                      We've selected smart defaults for you. Adjust only if you want specific changes.
                    </p>
                  </div>
                </div>
              </div>
              <div className="p-6 bg-slate-800/50 border-x border-b border-slate-700/40 rounded-b-lg">
                <div className="relative space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Budget Card */}
                    <motion.div variants={cardVariants} id="budget-section">
                      <StyledCard
                        variant="primary"
                        header={
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <ColoredIconContainer
                                icon={<DollarSign className="h-5 w-5" />}
                                colorScheme="green"
                              />
                              <span className="text-lg font-bold">Investment Budget</span>
                              <EducationalTooltip
                                title="Investment Budget"
                                description="Your investment budget determines how much capital you're allocating to your cryptocurrency portfolio. Different budget sizes impact diversification options and asset allocation strategies."
                                tips={[
                                  "Higher budgets allow for better diversification across more assets",
                                  "Start with amounts you can afford to lose when learning",
                                  "Consider allocating a fixed percentage of your overall investment funds"
                                ]}
                                type="info"
                                side="top"
                                iconSize="sm"
                              />
                            </div>
                          </div>
                        }
                        className="h-[320px]"
                      >
                        <div className="p-4 h-full flex flex-col">
                          <div className="grid grid-cols-3 gap-3 mb-4 mt-2">
                            {['low', 'medium', 'high'].map((budgetType) => (
                              <ActionButton
                                key={budgetType}
                                onClick={() => setBudget(budgetType)}
                                variant={budget === budgetType ? 'primary' : 'secondary'}
                                size="md"
                                className={`w-full shadow-sm shadow-slate-900/20 border ${budget === budgetType ? 'border-blue-500' : 'border-slate-700/40'} relative`}
                              >
                                {budgetType.charAt(0).toUpperCase() + budgetType.slice(1)}
                              </ActionButton>
                            ))}
                          </div>
                          <div className="space-y-3">
                            <FormInput
                              type="number"
                              placeholder="Enter custom amount"
                              value={customBudget}
                              onChange={(e) => setCustomBudget(e.target.value)}
                              prependLabel={selectedCurrency}
                              focusVariant="primary"
                              className="h-10"
                            />
                            <FormSelect
                              placeholder="Currency"
                              value={selectedCurrency}
                              onValueChange={setSelectedCurrency}
                              focusVariant="primary"
                              options={[
                                { label: 'USD - US Dollar', value: 'USD' },
                                { label: 'EUR - Euro', value: 'EUR' },
                                { label: 'GBP - British Pound', value: 'GBP' },
                                { label: 'BTC - Bitcoin', value: 'BTC' },
                                { label: 'ETH - Ethereum', value: 'ETH' }
                              ]}
                            />
                          </div>
                        </div>
                      </StyledCard>
                    </motion.div>

                    {/* Risk Profile Card */}
                    <motion.div variants={cardVariants} id="risk-profile-section">
                      <StyledCard
                        variant="primary"
                        header={
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <ColoredIconContainer
                                icon={<Shield className="h-5 w-5" />}
                                colorScheme="blue"
                              />
                              <span className="text-lg font-bold">Risk Profile</span>
                              <EducationalTooltip
                                title={investmentEducation.riskProfile.title}
                                description={investmentEducation.riskProfile.description}
                                tips={investmentEducation.riskProfile.tips}
                                learnMoreUrl={investmentEducation.riskProfile.learnMoreUrl}
                                type="info"
                                side="top"
                                iconSize="sm"
                              />
                            </div>
                          </div>
                        }
                        className="h-[320px]"
                      >
                        <div className="p-4 h-full flex flex-col">
                          <div className="grid grid-cols-3 gap-3 mt-2">
                            {[
                              { value: 'low', label: 'Low Risk' },
                              { value: 'balanced', label: 'Balanced' },
                              { value: 'high', label: 'High Risk' }
                            ].map((option) => (
                              <ActionButton
                                key={option.value}
                                onClick={() => setRiskProfile(option.value as RiskProfileType)}
                                variant={riskProfile === option.value ? 'primary' : 'secondary'}
                                size="md"
                                className={`w-full shadow-sm shadow-slate-900/20 border ${riskProfile === option.value ? 'border-blue-500' : 'border-slate-700/40'} relative`}
                              >
                                {option.label}
                              </ActionButton>
                            ))}
                          </div>
                        </div>
                      </StyledCard>
                    </motion.div>

                    {/* Trending Categories Card */}
                    <motion.div variants={cardVariants} id="sectors-section">
                      <StyledCard
                        variant="primary"
                        header={
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <ColoredIconContainer
                                icon={<Flame className="h-5 w-5" />}
                                colorScheme="amber"
                              />
                              <span className="text-lg font-bold">Trending Categories</span>
                              <EducationalTooltip
                                title="Cryptocurrency Sectors"
                                description="Cryptocurrency categories or sectors represent different use cases and technological focuses in the crypto ecosystem. Diversifying across sectors helps balance your portfolio."
                                tips={[
                                  "DeFi (Decentralized Finance) focuses on financial services",
                                  "Layer 2 solutions aim to improve blockchain scalability",
                                  "AI projects combine artificial intelligence with blockchain"
                                ]}
                                type="info"
                                side="top"
                                iconSize="sm"
                              />
                            </div>
                          </div>
                        }
                        className="h-[320px]"
                      >
                        <div className="p-4 h-full flex flex-col overflow-hidden">
                          <div className="space-y-4 mt-2 overflow-y-auto flex-1">
                            {/* Selected Categories */}
                            {selectedSectors.length > 0 && (
                              <div>
                                <div className="mb-2 flex items-center justify-between w-full">
                                  <FormLabel variant="primary" size="sm" className="mb-0">
                                    Selected categories ({selectedSectors.length}):
                                  </FormLabel>
                                  {selectedSectors.length >= 2 && (
                                    <button
                                      onClick={() => setSelectedSectors([])}
                                      className="text-xs text-red-400 hover:text-red-300 transition-colors cursor-pointer underline"
                                      type="button"
                                    >
                                      Clear selection
                                    </button>
                                  )}
                                </div>
                                <div className="flex flex-wrap gap-2">
                                  {selectedSectors.map((sector) => (
                                    <div
                                      key={sector}
                                      className="flex items-center gap-1.5 px-3 py-1.5 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg text-sm font-medium"
                                    >
                                      <span>{sector}</span>
                                      <button
                                        onClick={() => handleSectorToggle(sector)}
                                        className="hover:text-blue-300 transition-colors"
                                        aria-label={`Remove ${sector}`}
                                      >
                                        <X className="h-3.5 w-3.5" />
                                      </button>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Add Categories Dropdown */}
                            <div>
                              <FormLabel variant="primary" size="sm" className="mb-2">
                                Add categories:
                              </FormLabel>
                              {categoriesLoading ? (
                                <div className="flex items-center justify-center h-10 bg-slate-700/50 rounded-md border border-slate-600">
                                  <Loader2 className="h-4 w-4 animate-spin text-slate-400" />
                                  <span className="ml-2 text-xs text-slate-400">Loading categories...</span>
                                </div>
                              ) : categoriesError ? (
                                <div className="text-center h-10 bg-red-500/10 border border-red-500/30 rounded-md flex items-center justify-center">
                                  <span className="text-xs text-red-400">Failed to load categories</span>
                                </div>
                              ) : (
                                <FormSelect
                                  value=""
                                  onValueChange={(value: string) => {
                                    if (value && !selectedSectors.includes(value)) {
                                      handleSectorToggle(value);
                                    }
                                  }}
                                  placeholder="Select a category to add"
                                  focusVariant="primary"
                                  className="h-10"
                                  options={[...trendingSectors, ...additionalSectors]
                                    .filter(sector => !selectedSectors.includes(sector))
                                    .map((sector) => ({
                                      value: sector,
                                      label: sector
                                    }))}
                                />
                              )}
                              {selectedSectors.length === 0 && !categoriesLoading && (
                                <div className="mt-3 text-xs text-slate-500">
                                  Start by selecting categories from the dropdown above
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </StyledCard>
                    </motion.div>

                    {/* Investment Timeframe Card */}
                    <motion.div variants={cardVariants} id="timeframe-section">
                      <StyledCard
                        variant="primary"
                        header={
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <ColoredIconContainer
                                icon={<Clock className="h-5 w-5" />}
                                colorScheme="purple"
                              />
                              <span className="text-lg font-bold">Investment Timeframe</span>
                              <EducationalTooltip
                                title={investmentEducation.timeframe.title}
                                description={investmentEducation.timeframe.description}
                                tips={investmentEducation.timeframe.tips}
                                learnMoreUrl={investmentEducation.timeframe.learnMoreUrl}
                                type="info"
                                side="top"
                                iconSize="sm"
                              />
                            </div>
                          </div>
                        }
                        className="h-[320px]"
                      >
                        <div className="p-4 h-full flex flex-col">
                          <div className="grid grid-cols-3 gap-3 mt-2">
                            {[
                              { value: 'short', label: 'Short Term' },
                              { value: 'medium', label: 'Medium Term' },
                              { value: 'long', label: 'Long Term' }
                            ].map((option) => (
                              <ActionButton
                                key={option.value}
                                onClick={() => setTimeframe(option.value)}
                                variant={timeframe === option.value ? 'primary' : 'secondary'}
                                size="md"
                                className={`w-full shadow-sm shadow-slate-900/20 border ${timeframe === option.value ? 'border-blue-500' : 'border-slate-700/40'} relative`}
                              >
                                {option.label}
                              </ActionButton>
                            ))}
                          </div>
                        </div>
                      </StyledCard>
                    </motion.div>
                  </div>


                  <div className="mt-6 pt-4 border-t border-slate-700/40">
                    {/* Terms of Service Checkbox */}


                    <motion.div
                      className="flex flex-col sm:flex-row justify-between items-center gap-6"
                      variants={cardVariants}
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-3">
                          <ActionButton
                            onClick={handleGeneratePortfolio}
                            variant="primary"
                            size="md"
                            className="min-w-[180px]"
                            disabled={isGenerating}
                            icon={isGenerating ? <Loader2 className="animate-spin" /> : undefined}
                          >
                            {isGenerating ? 'Generating...' : 'Generate Portfolio'}
                          </ActionButton>

                          {/* Reset Button */}
                          <ActionButton
                            onClick={() => setShowResetConfirm(true)}
                            variant="secondary"
                            size="md"
                            className="min-w-[180px]"
                            title="Reset all settings to defaults"
                            icon={<RotateCcw />}
                          >
                            Reset
                          </ActionButton>
                        </div>

                        {/* Save Preferences Button */}
                        <ActionButton
                          onClick={handleSavePreferences}
                          variant="secondary"
                          size="md"
                          className="min-w-[180px]"
                          title="Save current settings as default"
                          icon={<Save />}
                        >
                          Save Preferences
                        </ActionButton>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>
            )}

            {/* Results Section Separator and Header */}
            {showResults && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ type: "spring", stiffness: 300, damping: 30, duration: 0.4 }}
                className="mb-8"
              >
                {/* Congratulations Message */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.2, duration: 0.5 }}
                  className="text-center mb-8 bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/30 rounded-lg p-6 shadow-sm shadow-slate-900/20"
                >
                  <div className="flex items-center justify-center gap-3 mb-3">
                    <CheckCircle className="h-8 w-8 text-green-500" />
                    <h3 className="text-2xl font-bold text-white">
                      {currentLanguage?.code === 'tr'
                        ? 'Portföy Şablonu Başarıyla Oluşturuldu!'
                        : 'Portfolio Template Generated Successfully!'}
                    </h3>
                  </div>
                  <p className="text-slate-300 mb-4">
                    Your educational portfolio has been created based on your preferences.
                    Review the allocation below and save it for future reference.
                  </p>
                  <div className="flex justify-center gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-slate-400">{portfolioAllocation.length} Assets Selected</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-slate-400">Risk Profile: {riskProfile.charAt(0).toUpperCase() + riskProfile.slice(1)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span className="text-slate-400">Timeframe: {timeframe.charAt(0).toUpperCase() + timeframe.slice(1)} Term</span>
                    </div>
                  </div>
                </motion.div>

                {/* Save/Share/Export Controls */}
                <StyledCard variant="primary" className="flex items-center gap-3 p-4 backdrop-blur-md mb-6 overflow-x-auto">
                  {/* Action Buttons - All in a single line */}
                  <div className="flex items-center gap-2 flex-shrink-0 w-full">
                    {/* Portfolio Dropdown - Only shows saved portfolios */}
                    <div className="flex-shrink-0" style={{ minWidth: '250px' }}>
                      <Select
                        value={selectedPortfolioId || ""}
                        onValueChange={(value) => {
                          handleLoadPortfolio(value);
                        }}
                      >
                        <SelectTrigger className="h-10 bg-slate-900/50 border-slate-700/40 w-full">
                          <SelectValue placeholder="Select saved portfolio">
                            {selectedPortfolioId
                              ? portfolioName
                              : (portfolioName || "Current Generated Portfolio")}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          {savedPortfolios.length > 0 ? (
                            savedPortfolios.map((portfolio) => (
                              <SelectItem key={portfolio.id} value={portfolio.id} className="hover:bg-slate-700">
                                <div className="flex items-center justify-between w-full gap-2">
                                  <span className="flex-grow">{portfolio.name}</span>
                                  <span className="text-xs text-slate-400">
                                    {new Date(portfolio.lastModified).toLocaleDateString()}
                                  </span>
                                </div>
                              </SelectItem>
                            ))
                          ) : (
                            <div className="p-4 text-center text-slate-400 text-sm">
                              No saved portfolios yet. Generate and save your first portfolio!
                            </div>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Portfolio Actions Dropdown */}
                    {selectedPortfolioId && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <ActionButton
                            variant="secondary"
                            size="sm"
                            className="h-10 w-10 p-0 flex items-center justify-center flex-shrink-0 hover:bg-primary hover:text-white hover:border-primary"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </ActionButton>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="bg-slate-800 border-slate-700" align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setEditingPortfolioName(portfolioName);
                              setShowRenameDialog(true);
                            }}
                            className="hover:bg-slate-700 cursor-pointer"
                          >
                            <Edit2 className="h-4 w-4 mr-2" />
                            Rename
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setPortfolioToDelete(selectedPortfolioId);
                              setShowDeleteConfirm(true);
                            }}
                            className="hover:bg-slate-700 cursor-pointer text-red-400"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}

                    {/* Separator */}
                    <div className="w-px h-8 bg-slate-700/40 mx-1" />

                    <StandardTooltip
                      content="Generate a new portfolio from scratch"
                      side="top"
                      align="center"
                    >
                      <ActionButton
                        onClick={handleCreateNewPortfolio}
                        variant="secondary"
                        size="sm"
                        className="h-10 hover:bg-primary hover:text-white hover:border-primary"
                        icon={<Plus />}
                      >
                        Generate New Portfolio
                      </ActionButton>
                    </StandardTooltip>

                    {/* Separator */}
                    <div className="w-px h-8 bg-slate-700/40 mx-1" />

                    <StandardTooltip
                      content="Save this portfolio"
                      side="top"
                      align="center"
                    >
                      {user ? (
                        <ActionButton
                          onClick={handleSavePortfolio}
                          disabled={isSaving}
                          variant="secondary"
                          size="sm"
                          className="h-10 hover:bg-primary hover:text-white hover:border-primary"
                          icon={<Save />}
                        >
                          {isSaving ? 'Saving...' : 'Save'}
                        </ActionButton>
                      ) : (
                        <LoginPromptButton
                          feature="Portfolio Saving"
                          onClick={handleSavePortfolio}
                          variant="default"
                          size="sm"
                          className="flex items-center gap-2"
                        >
                          <Save className="h-4 w-4" />
                          <span>Save</span>
                        </LoginPromptButton>
                      )}
                    </StandardTooltip>

                    <StandardTooltip
                      content="Share portfolio with others"
                      side="top"
                      align="center"
                    >
                      <ActionButton
                        onClick={handleSharePortfolio}
                        variant="secondary"
                        size="sm"
                        className="h-10 hover:bg-primary hover:text-white hover:border-primary"
                        icon={<Share2 />}
                      >
                        Share
                      </ActionButton>
                    </StandardTooltip>

                    <StandardTooltip
                      content="Export as PDF report"
                      side="top"
                      align="center"
                    >
                      <ActionButton
                        onClick={handleExportPDF}
                        variant="secondary"
                        size="sm"
                        className="h-10 hover:bg-primary hover:text-white hover:border-primary"
                        icon={<FileDown />}
                      >
                        Export
                      </ActionButton>
                    </StandardTooltip>
                  </div>
                </StyledCard>
              </motion.div>
            )}

            {/* Portfolio Results Grid */}
            {showResults && (
              <motion.div
                id="portfolio-results"
                variants={cardVariants}
                className="grid gap-6"
              >






                        {/* Portfolio Assets Table Section */}
                        <motion.div
                          className="relative rounded-lg overflow-hidden shadow-md shadow-slate-900/40"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 30,
                            duration: 0.4,
                            delay: 0.2
                          }}
                        >
                          <StyledCard
                            variant="primary"
                            header={
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <ColoredIconContainer
                                      icon={<BarChart2 className="h-5 w-5" />}
                                      colorScheme="blue"
                                    />
                                    <span className="text-lg font-bold">Complete Asset Breakdown</span>
                                    <EducationalTooltip
                                      title={investmentEducation.allocation.title}
                                      description={investmentEducation.allocation.description}
                                      tips={[
                                        "Each asset's weight affects overall portfolio performance",
                                        "Higher allocation to an asset increases its impact on returns and risk",
                                        "Monitor allocation percentages to maintain your desired strategy",
                                        "Click on any asset to see detailed information and analysis"
                                      ]}
                                      learnMoreUrl={investmentEducation.allocation.learnMoreUrl}
                                      type="info"
                                      side="top"
                                      iconSize="sm"
                                    />
                                  </div>
                                </div>
                              }
                            >
                              {/* Using the GeneratorPortfolioTable component (Generator-specific) */}
                              <div className="mt-2">
                                {generatedPortfolio && (
                                  <GeneratorPortfolioTable
                                    portfolio={generatedPortfolio}
                                    onAssetClick={(asset: any) => {
                                      console.log('Asset clicked:', asset);
                                      // Navigate to the coin details page for this asset
                                      window.location.href = `/coin/${asset.id}`;
                                    }}
                                    variant="default"
                                    showCharts={true}
                                    hideHeading={true}
                                  />
                                )}
                              </div>
                            </StyledCard>
                        </motion.div>


                        {/* Portfolio Allocation Analysis Section */}
                        <motion.div
                          className="relative rounded-lg overflow-hidden shadow-md shadow-slate-900/40"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 30,
                            duration: 0.4,
                            delay: 0.4
                          }}
                        >
                          <StyledCard variant="primary">
                            {/* Using the PortfolioAllocationAnalysis component */}
                            {generatedPortfolio && (
                              <PortfolioAllocationAnalysis
                                className="p-6"
                                portfolio={generatedPortfolio}
                              />
                            )}
                          </StyledCard>
                </motion.div>
              </motion.div>
            )}

          </StyledCard>
        </div>
      </div>

      {/* Hidden element for snapshot capture */}
      <div style={{ position: 'absolute', left: '-9999px', top: 0, width: '800px', overflow: 'hidden', opacity: showSnapshotDialog ? 1 : 0 }}>
        <PortfolioSnapshotWrapper
          id="portfolio-snapshot-template"
          portfolioName={portfolioName || 'My Portfolio'}
          assets={portfolioAllocation}
          riskProfile={riskProfile}
          timeframe={timeframe}
          riskScore={portfolioMetrics.riskScore.value}
          diversificationScore={portfolioMetrics.diversification.value}
          totalValue={portfolioStats.totalValue.value}
        />

        {/* Educational Purpose Notice - Moved to bottom */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-8 mb-4 p-4 bg-amber-500/10 border border-amber-500/30 rounded-lg"
        >
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-amber-200">
              <p className="font-semibold mb-1">Educational Purpose Notice</p>
              <p className="text-amber-200/80 leading-relaxed">
                This is an educational tool designed to help you learn about portfolio diversification and cryptocurrency allocation strategies.
                The AI-generated portfolios are for educational purposes only and should not be used as investment advice.
                Always conduct thorough research and consult with qualified professionals before making any financial decisions.
              </p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Portfolio snapshot dialog */}
      <PortfolioSnapshotDialog
        open={showSnapshotDialog}
        onOpenChange={setShowSnapshotDialog}
        portfolioName={portfolioName || 'My Portfolio'}
        portfolioElementId="portfolio-snapshot-template"
      />

      {/* Export Modal */}
      <SharedExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        title="Export Portfolio"
        description="Choose your export format and customize what to include in your report"
        sections={exportSections}
        formatOptions={exportFormatOptions}
        onExport={handleExportAction}
        portfolioName="AI Portfolio"
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent className="bg-slate-800 text-white border border-slate-700">
          <AlertDialogHeader>
            <div className="flex items-center gap-3 mb-2">
              <div className="h-10 w-10 rounded-full bg-red-500/20 flex items-center justify-center">
                <Trash2 className="h-5 w-5 text-red-500" />
              </div>
              <AlertDialogTitle className="text-white text-lg font-semibold">
                Delete Portfolio
              </AlertDialogTitle>
            </div>
            <AlertDialogDescription className="text-slate-300 space-y-2">
              <p>
                Are you sure you want to delete "{portfolioToDelete && savedPortfolios.find(p => p.id === portfolioToDelete)?.name}"?
              </p>
              <p className="text-sm text-red-400 font-medium">
                ⚠️ This action cannot be undone. The portfolio and all its data will be permanently removed.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-slate-700 hover:bg-slate-600 text-white border-slate-600">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePortfolio}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete Portfolio
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Rename Portfolio Dialog */}
      <AlertDialog open={showRenameDialog} onOpenChange={setShowRenameDialog}>
        <AlertDialogContent className="bg-slate-800 text-white border border-slate-700">
          <AlertDialogHeader>
            <div className="flex items-center gap-3 mb-2">
              <div className="h-10 w-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                <Edit2 className="h-5 w-5 text-blue-500" />
              </div>
              <AlertDialogTitle className="text-white text-lg font-semibold">
                Rename Portfolio
              </AlertDialogTitle>
            </div>
            <AlertDialogDescription className="text-slate-300">
              Enter a new name for your portfolio
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <input
              type="text"
              value={editingPortfolioName}
              onChange={(e) => setEditingPortfolioName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleRenamePortfolio();
                }
              }}
              className="w-full px-3 py-2 bg-slate-900/50 border border-slate-700/40 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
              placeholder="Enter portfolio name"
              autoFocus
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel
              className="bg-slate-700 hover:bg-slate-600 text-white border-slate-600"
              onClick={() => setEditingPortfolioName('')}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRenamePortfolio}
              className="bg-primary hover:bg-primary/90 text-white"
              disabled={!editingPortfolioName.trim()}
            >
              Rename
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Reset Confirmation Dialog */}
      <AlertDialog open={showResetConfirm} onOpenChange={setShowResetConfirm}>
        <AlertDialogContent className="bg-slate-800 text-white border border-slate-700">
          <AlertDialogHeader>
            <div className="flex items-center gap-3 mb-2">
              <div className="h-10 w-10 rounded-full bg-amber-500/20 flex items-center justify-center">
                <RotateCcw className="h-5 w-5 text-amber-500" />
              </div>
              <AlertDialogTitle className="text-white text-lg font-semibold">
                Reset All Settings
              </AlertDialogTitle>
            </div>
            <AlertDialogDescription className="text-slate-300">
              This will reset all configuration settings to their default values. Your saved portfolios will not be affected.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-slate-700 hover:bg-slate-600 text-white border-slate-600">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                handleResetAll();
                setShowResetConfirm(false);
              }}
              className="bg-amber-600 hover:bg-amber-700 text-white"
            >
              Reset Settings
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Generate Confirmation Dialog */}
      <AlertDialog open={showGenerateConfirm} onOpenChange={setShowGenerateConfirm}>
        <AlertDialogContent className="bg-slate-800 text-white border border-slate-700">
          <AlertDialogHeader>
            <div className="flex items-center gap-3 mb-2">
              <div className="h-10 w-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                <Brain className="h-5 w-5 text-blue-500" />
              </div>
              <AlertDialogTitle className="text-white text-lg font-semibold">
                Generate New Portfolio
              </AlertDialogTitle>
            </div>
            <AlertDialogDescription className="text-slate-300">
              This will create a new portfolio and replace the current one. Make sure to save your current portfolio if you want to keep it.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-slate-700 hover:bg-slate-600 text-white border-slate-600">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                setShowGenerateConfirm(false);
                handleGeneratePortfolio();
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Generate New
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Save Portfolio Modal */}
      <AlertDialog open={showSavePortfolioModal} onOpenChange={setShowSavePortfolioModal}>
        <AlertDialogContent className="bg-slate-800 text-white border border-slate-700 sm:max-w-md">
          <AlertDialogHeader>
            <div className="flex items-center gap-3 mb-2">
              <div className="h-10 w-10 rounded-full bg-green-500/20 flex items-center justify-center">
                <Save className="h-5 w-5 text-green-500" />
              </div>
              <AlertDialogTitle className="text-white text-lg font-semibold">
                {currentLanguage?.code === 'tr'
                  ? 'Portföy Şablonunu Kaydet'
                  : 'Save Portfolio Template'}
              </AlertDialogTitle>
            </div>
            <AlertDialogDescription className="text-slate-300">
              {currentLanguage?.code === 'tr'
                ? 'Portföy şablonunu gerçek portföye dönüştürmek için gerekli bilgileri girin.'
                : 'Enter the required information to convert your portfolio template into a real portfolio.'}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="portfolio-name" className="text-sm font-medium text-slate-200">
                {currentLanguage?.code === 'tr' ? 'Portföy İsmi' : 'Portfolio Name'}
              </Label>
              <Input
                id="portfolio-name"
                value={savePortfolioData.name}
                onChange={(e) => setSavePortfolioData(prev => ({...prev, name: e.target.value}))}
                className="w-full px-3 py-2 bg-slate-900/50 border border-slate-700/40 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                placeholder={currentLanguage?.code === 'tr' ? 'Portföy ismini girin' : 'Enter portfolio name'}
                autoFocus
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="portfolio-total-value" className="text-sm font-medium text-slate-200">
                {currentLanguage?.code === 'tr' ? 'Toplam Değer (USD)' : 'Total Value (USD)'}
              </Label>
              <Input
                id="portfolio-total-value"
                type="number"
                value={savePortfolioData.totalValue}
                onChange={(e) => setSavePortfolioData(prev => ({...prev, totalValue: e.target.value}))}
                className="w-full px-3 py-2 bg-slate-900/50 border border-slate-700/40 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                placeholder="5000"
                min="0"
                step="100"
              />
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel
              className="bg-slate-700 hover:bg-slate-600 text-white border-slate-600"
              onClick={() => setSavePortfolioData({ name: '', totalValue: '' })}
            >
              {currentLanguage?.code === 'tr' ? 'İptal' : 'Cancel'}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleSavePortfolioConfirm}
              className="bg-green-600 hover:bg-green-700 text-white"
              disabled={isSaving || !savePortfolioData.name.trim() || !savePortfolioData.totalValue.trim()}
            >
              {isSaving
                ? (currentLanguage?.code === 'tr' ? 'Kaydediliyor...' : 'Saving...')
                : (currentLanguage?.code === 'tr' ? 'Kaydet' : 'Save')
              }
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Save Portfolio Modal */}
      <Dialog open={isSaveModalOpen} onOpenChange={setIsSaveModalOpen}>
        <DialogContent className="bg-slate-800 border-slate-700 text-white max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-white">
              {currentLanguage?.code === 'tr' ? 'Portfolio Kaydet' : 'Save Portfolio'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="modal-portfolio-name" className="text-sm font-medium text-slate-200">
                {currentLanguage?.code === 'tr' ? 'Portfolio Adı' : 'Portfolio Name'}
              </Label>
              <Input
                id="modal-portfolio-name"
                value={modalPortfolioName}
                onChange={(e) => setModalPortfolioName(e.target.value)}
                className="w-full px-3 py-2 bg-slate-900/50 border border-slate-700/40 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                placeholder={currentLanguage?.code === 'tr' ? 'Portfolio adını girin' : 'Enter portfolio name'}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="modal-budget" className="text-sm font-medium text-slate-200">
                {currentLanguage?.code === 'tr' ? 'Bütçe (USD)' : 'Budget (USD)'}
              </Label>
              <Input
                id="modal-budget"
                type="number"
                value={modalBudget}
                onChange={(e) => setModalBudget(e.target.value)}
                className="w-full px-3 py-2 bg-slate-900/50 border border-slate-700/40 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                placeholder="5000"
                min="0"
                step="100"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsSaveModalOpen(false)}
              className="bg-slate-700 hover:bg-slate-600 text-white border-slate-600"
            >
              {currentLanguage?.code === 'tr' ? 'İptal' : 'Cancel'}
            </Button>
            <Button
              onClick={handleModalSave}
              disabled={isSaving || !modalPortfolioName.trim() || !modalBudget.trim()}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {isSaving
                ? (currentLanguage?.code === 'tr' ? 'Kaydediliyor...' : 'Saving...')
                : (currentLanguage?.code === 'tr' ? 'Kaydet' : 'Save')
              }
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
}