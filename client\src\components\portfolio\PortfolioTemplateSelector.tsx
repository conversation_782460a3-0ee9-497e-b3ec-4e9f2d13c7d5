import React, { useState } from 'react';
import { 
  PortfolioTemplate
} from '@/data/portfolioTemplates';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { StandardTooltip } from '@/components/shared/StandardTooltip';
import { ColoredIconContainer } from '@/components/shared/ColoredIconContainer';
import { EducationalTooltip } from '@/components/shared/EducationalTooltip';
import { 
  ShieldCheck, 
  Clock, 
  Heart,
  BarChart3, 
  ChevronRight, 
  Check, 
  X, 
  User,
  Filter,
  Sparkles,
  Info
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface PortfolioTemplateSelectorProps {
  templates?: PortfolioTemplate[];
  onSelectTemplate: (template: PortfolioTemplate) => void;
  onToggleLike?: (templateId: string) => void;
  isApplying?: boolean;
  className?: string;
}

export const PortfolioTemplateSelector: React.FC<PortfolioTemplateSelectorProps> = ({ 
  templates = [],
  onSelectTemplate,
  onToggleLike,
  isApplying = false,
  className 
}) => {
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
  const [likedTemplates, setLikedTemplates] = useState<Set<string>>(new Set());
  
  const selectedTemplate = selectedTemplateId 
    ? templates.find(t => t.id === selectedTemplateId) || null
    : null;
  
  // Risk level badge color
  const getRiskBadgeColor = (risk: string) => {
    switch (risk) {
      case 'low':
        return 'bg-emerald-500/20 text-emerald-400 border-emerald-500/30';
      case 'medium':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'high':
        return 'bg-amber-500/20 text-amber-400 border-amber-500/30';
      default:
        return 'bg-slate-500/20 text-slate-400 border-slate-500/30';
    }
  };
  
  // Timeframe badge color
  const getTimeframeBadgeColor = (timeframe: string) => {
    switch (timeframe) {
      case 'short':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'medium':
        return 'bg-indigo-500/20 text-indigo-400 border-indigo-500/30';
      case 'long':
        return 'bg-teal-500/20 text-teal-400 border-teal-500/30';
      default:
        return 'bg-slate-500/20 text-slate-400 border-slate-500/30';
    }
  };
  
  const handleSelectTemplate = (template: PortfolioTemplate) => {
    setSelectedTemplateId(template.id);
    
    // Smooth scroll to template details after selection
    setTimeout(() => {
      const element = document.querySelector('[data-template-details]');
      if (element) {
        const targetPosition = element.getBoundingClientRect().top + window.pageYOffset - 100;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        const duration = 1000; // 1 second for smooth scrolling
        let start: number | null = null;
        
        const animation = (currentTime: number) => {
          if (start === null) start = currentTime;
          const timeElapsed = currentTime - start;
          const progress = Math.min(timeElapsed / duration, 1);
          
          // Easing function for smooth deceleration
          const easeInOutCubic = progress < 0.5 
            ? 4 * progress * progress * progress 
            : 1 - Math.pow(-2 * progress + 2, 3) / 2;
          
          window.scrollTo(0, startPosition + distance * easeInOutCubic);
          
          if (timeElapsed < duration) {
            requestAnimationFrame(animation);
          }
        };
        
        requestAnimationFrame(animation);
      }
    }, 100);
  };
  
  const handleApplyTemplate = () => {
    if (selectedTemplate) {
      console.log('🔥 [PortfolioTemplateSelector] Applying template:', selectedTemplate.name);
      console.log('🔥 [PortfolioTemplateSelector] Template allocation:', selectedTemplate.allocation);
      onSelectTemplate(selectedTemplate);
    } else {
      console.error('❌ [PortfolioTemplateSelector] No template selected for application');
    }
  };
  
  const handleLikeTemplate = (templateId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent card selection when clicking like button
    
    if (onToggleLike) {
      onToggleLike(templateId);
    } else {
      // Fallback to local state for demo purposes
      setLikedTemplates(prev => {
        const newLiked = new Set(prev);
        if (newLiked.has(templateId)) {
          newLiked.delete(templateId);
        } else {
          newLiked.add(templateId);
        }
        return newLiked;
      });
    }
  };

  const handleCancelTemplate = () => {
    // Smooth scroll back to templates grid
    const element = document.querySelector('[data-templates-grid]');
    if (element) {
      const targetPosition = element.getBoundingClientRect().top + window.pageYOffset - 100;
      const startPosition = window.pageYOffset;
      const distance = targetPosition - startPosition;
      const duration = 800; // Slightly faster for going back up
      let start: number | null = null;
      
      const animation = (currentTime: number) => {
        if (start === null) start = currentTime;
        const timeElapsed = currentTime - start;
        const progress = Math.min(timeElapsed / duration, 1);
        
        // Easing function for smooth deceleration
        const easeInOutCubic = progress < 0.5 
          ? 4 * progress * progress * progress 
          : 1 - Math.pow(-2 * progress + 2, 3) / 2;
        
        window.scrollTo(0, startPosition + distance * easeInOutCubic);
        
        if (timeElapsed < duration) {
          requestAnimationFrame(animation);
        } else {
          // Clear selection after scroll completes
          setSelectedTemplateId(null);
        }
      };
      
      requestAnimationFrame(animation);
    } else {
      // Fallback if element not found
      setSelectedTemplateId(null);
    }
  };
  
  // Separate recommended and regular templates
  const recommendedTemplates = templates.filter(template => template.isRecommended);
  const regularTemplates = templates.filter(template => !template.isRecommended);

  const renderTemplateCard = (template: PortfolioTemplate, isRecommended = false) => (
    <motion.div
      key={template.id}
      whileHover={{ y: -5 }}
      transition={{ type: 'spring', stiffness: 300, damping: 20 }}
      className={cn(
        "rounded-lg border transition-all duration-200",
        selectedTemplateId === template.id
          ? "border-blue-500/50 bg-blue-500/10 shadow-md shadow-blue-500/10"
          : isRecommended
            ? "border-amber-500/40 bg-gradient-to-br from-amber-500/5 to-orange-500/5 hover:border-amber-500/60 hover:from-amber-500/10 hover:to-orange-500/10 shadow-sm shadow-amber-500/5"
            : "border-slate-700/40 bg-slate-800/50 hover:border-slate-600/70"
      )}
    >
      <Card className="bg-transparent border-0 shadow-none overflow-hidden">
        <CardContent className="p-4">
          <div className="flex flex-col">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                <h4 className={cn(
                  "font-medium text-base",
                  isRecommended && "text-amber-200"
                )}>
                  {template.name}
                </h4>
                {isRecommended && (
                  <Badge 
                    variant="outline" 
                    className="text-[10px] py-0 bg-amber-500/20 text-amber-400 border-amber-500/40"
                  >
                    <Sparkles className="h-2.5 w-2.5 mr-1" />
                    Featured
                  </Badge>
                )}
              </div>
              <Button
                variant={selectedTemplateId === template.id ? "default" : "outline"}
                size="sm"
                className={cn(
                  "h-7 text-xs",
                  selectedTemplateId === template.id
                    ? "bg-blue-500/90 hover:bg-blue-500 text-white"
                    : isRecommended
                      ? "text-amber-300 border-amber-500/40 hover:bg-amber-500 hover:text-white hover:border-amber-500"
                      : "text-slate-300 hover:bg-blue-500 hover:text-white hover:border-blue-500"
                )}
                onClick={() => {
                  if (selectedTemplateId === template.id) {
                    console.log('🔥 [PortfolioTemplateSelector] Direct apply for template:', template.name);
                    onSelectTemplate(template);
                  } else {
                    console.log('🔥 [PortfolioTemplateSelector] Selecting template:', template.name);
                    handleSelectTemplate(template);
                  }
                }}
              >
                {selectedTemplateId === template.id ? (
                  <>
                    <Check className="h-3.5 w-3.5 mr-1" />
                    Apply
                  </>
                ) : (
                  <>
                    <Sparkles className="h-3.5 w-3.5 mr-1" />
                    Select
                  </>
                )}
              </Button>
            </div>
            
            <p className={cn(
              "text-xs mt-1 line-clamp-2 min-h-[2rem]",
              isRecommended ? "text-slate-300" : "text-slate-400"
            )}>
              {template.description}
            </p>
            
            {/* Like Button and Count */}
            <div className="flex items-center justify-between mt-3">
              <div className="flex items-center flex-wrap gap-2">
                <Badge 
                  variant="outline" 
                  className={cn("text-[10px] py-0", 
                    getRiskBadgeColor(template.riskLevel)
                  )}
                >
                  <ShieldCheck className="h-3 w-3 mr-1" />
                  {template.riskLevel.charAt(0).toUpperCase() + template.riskLevel.slice(1)} Risk
                </Badge>
                
                <Badge 
                  variant="outline" 
                  className={cn("text-[10px] py-0", 
                    getTimeframeBadgeColor(template.timeframe)
                  )}
                >
                  <Clock className="h-3 w-3 mr-1" />
                  {template.timeframe.charAt(0).toUpperCase() + template.timeframe.slice(1)} Term
                </Badge>
              </div>

              {/* Like Button and Count */}
              <div className="flex items-center gap-1">
                <button
                  onClick={(e) => handleLikeTemplate(template.id, e)}
                  className={cn(
                    "flex items-center gap-1 px-2 py-1 rounded-md transition-all duration-200 hover:bg-slate-700/50",
                    (template.isLikedByUser || likedTemplates.has(template.id))
                      ? "text-red-400"
                      : "text-slate-400 hover:text-red-400"
                  )}
                  data-testid={`button-like-${template.id}`}
                >
                  <Heart 
                    className={cn(
                      "h-3.5 w-3.5 transition-all duration-200",
                      (template.isLikedByUser || likedTemplates.has(template.id))
                        ? "fill-current text-red-400"
                        : "text-slate-400"
                    )}
                  />
                  <span 
                    className="text-xs font-medium" 
                    data-testid={`text-like-count-${template.id}`}
                  >
                    {template.likeCount + (likedTemplates.has(template.id) && !template.isLikedByUser ? 1 : 
                                         !likedTemplates.has(template.id) && template.isLikedByUser ? -1 : 0)}
                  </span>
                </button>
              </div>
            </div>
            
            <div className="mt-3 pt-3 border-t border-slate-700/40">
              <div className="text-xs font-medium mb-1.5">Key assets:</div>
              <div className="flex flex-wrap gap-1.5 text-[10px]">
                {template.allocation.slice(0, 5).map((asset) => (
                  <Badge 
                    key={asset.symbol} 
                    variant="outline" 
                    className="py-0 h-4 bg-slate-800/50 border-slate-600/30 text-slate-400"
                  >
                    {asset.symbol}
                  </Badge>
                ))}
                {template.allocation.length > 5 && (
                  <Badge 
                    variant="outline" 
                    className="py-0 h-4 bg-slate-800/50 border-slate-600/30 text-slate-400"
                  >
                    +{template.allocation.length - 5} more
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <div className={cn("space-y-6", className)}>
      {/* Recommended Templates Section */}
      {recommendedTemplates.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center gap-3 pb-3 border-b border-amber-500/20">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 rounded-full bg-amber-500/20 flex items-center justify-center">
                <Sparkles className="h-3.5 w-3.5 text-amber-400" />
              </div>
              <h3 className="text-lg font-semibold text-amber-200">The Coinscout Collection</h3>
            </div>
            <Badge 
              variant="outline" 
              className="text-xs bg-amber-500/10 text-amber-400 border-amber-500/30"
            >
              Curated by Experts
            </Badge>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" data-recommended-templates>
            {recommendedTemplates.map((template) => renderTemplateCard(template, true))}
          </div>
        </div>
      )}

      {/* Regular Templates Section */}
      {regularTemplates.length > 0 && (
        <div className="space-y-4">
          {recommendedTemplates.length > 0 && (
            <div className="flex items-center gap-3 pb-3 border-b border-slate-700/40">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-slate-500/20 flex items-center justify-center">
                  <BarChart3 className="h-3.5 w-3.5 text-slate-400" />
                </div>
                <h3 className="text-lg font-semibold text-slate-200">All Templates</h3>
              </div>
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" data-templates-grid>
            {regularTemplates.map((template) => renderTemplateCard(template, false))}
          </div>
        </div>
      )}
      
      {selectedTemplate && (
        <motion.div
          data-template-details
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-6 bg-slate-800/70 border border-slate-700/40 rounded-lg overflow-hidden"
        >
          <div className="p-4 border-b border-slate-700/40 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <ColoredIconContainer
                icon={<BarChart3 className="h-4 w-4" />}
                colorScheme="blue"
                size="sm"
              />
              <h3 className="font-medium">Template Details: {selectedTemplate.name}</h3>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="text-slate-300 hover:bg-blue-500 hover:text-white hover:border-blue-500 transition-colors"
                onClick={handleCancelTemplate}
              >
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
              <Button
                variant="default"
                className="bg-blue-500 hover:bg-blue-600 text-white"
                onClick={handleApplyTemplate}
              >
                Apply Template <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="p-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2 space-y-4">
              <div>
                <h4 className="text-sm font-medium text-slate-300">Strategy</h4>
                <p className="text-sm text-slate-400 mt-1">{selectedTemplate.strategy}</p>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-slate-300">Suitable For</h4>
                  <ul className="mt-1 space-y-1">
                    {selectedTemplate.suitableFor.map((item, index) => (
                      <li key={index} className="flex items-start gap-2 text-xs text-slate-400">
                        <span className="mt-0.5"><Check className="h-3 w-3 text-emerald-400" /></span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium text-slate-300">Not Suitable For</h4>
                  <ul className="mt-1 space-y-1">
                    {selectedTemplate.notSuitableFor.map((item, index) => (
                      <li key={index} className="flex items-start gap-2 text-xs text-slate-400">
                        <span className="mt-0.5"><X className="h-3 w-3 text-red-400" /></span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
            
            <div className="bg-slate-800/90 rounded-lg border border-slate-700/40 p-3">
              <h4 className="text-sm font-medium text-slate-300 mb-2">Allocation</h4>
              <div className="space-y-2 max-h-[250px] overflow-y-auto pr-2">
                {selectedTemplate.allocation.map((asset) => (
                  <div 
                    key={asset.symbol} 
                    className="flex items-center justify-between p-2 rounded-md bg-slate-800 border border-slate-700/40"
                  >
                    <div className="flex items-center gap-2">
                      <div 
                        className={cn(
                          "w-2 h-2 rounded-full", 
                          asset.color.replace('bg-', 'bg-')
                        )}
                      />
                      <span className="text-xs font-medium">{asset.symbol}</span>
                      <span className="text-xs text-slate-400">{asset.fullName}</span>
                    </div>
                    <span className="text-xs font-medium">{asset.percentage}%</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};