import React from 'react';
import { Card, CardContent } from "../ui/card";
import { Skeleton } from "../ui/skeleton";
import { Portfolio, PortfolioAsset } from '../../lib/types';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip as RechartsTooltip } from 'recharts';
import { PORTFOLIO_COLORS, OPACITY_VALUES, SPACE } from '../../lib/design-tokens';
import { Progress } from "../ui/progress";
import {
  calculateMarketCapAllocations,
  getMarketCapCategory,
  formatMarketCapAllocationsForChart,
  MARKET_CAP_RECOMMENDED_ALLOCATIONS,
  MARKET_CAP_COLORS
} from '../../lib/market-cap-utils';
import {
  getTrendingSector,
  calculateSectorAllocations,
  TRENDING_SECTORS,
  SECTOR_BENCHMARK_ALLOCATIONS,
  SECTOR_PERFORMANCE_SCORES,
  TrendingSector,
  formatTrendingForHorizontalBarChart,
  formatTrendingSectorsForCards
} from '../../lib/trending-sectors-utils';
// CSS animations used instead of framer-motion
import {
  Tooltip as UITooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "../ui/tooltip";
import {
  getDefaultColor,
  formatPercent,
  getRiskColor,
  getRiskColorBg,
  groupAssets,
  calculateCoinScoutScore
} from "../../lib/portfolio-utils";

// Define MetricType locally since it's not exported
type MetricType = 'asset' | 'market-cap' | 'category-diversification' | 'trending-bar-chart' | 'investment-balance' | 'stablecoin-ratio' | 'portfolio-quality' | 'overall-portfolio' | 'coinscore';
import { MetricTab, ChartDataItem } from './types';
// import RecommendationsSection, { createRecommendation } from './RecommendationsSection'; // Disabled - TypeScript stub only
// import { EnhancedCoinScoutDashboard } from './EnhancedCoinScoutDashboard'; // Disabled - TypeScript stub only
import {
  PieChart as PieChartIcon,
  Layers,
  CircleDollarSign,
  BarChart2,
  BarChart,
  BarChart3,
  ChartBar,
  Coins,
  Zap,
  AlertCircle,
  DollarSign,
  Shield,
  ArrowUp,
  TrendingDown,
  Briefcase,
  LineChart,
  Percent,
  Scale,
  Globe,
  TrendingUp,
  Activity,
  AlertTriangle,
  BadgeCheck,
  Info,
  Lightbulb,
  MoveUp,
  Award,
  FilterX,
  Plus,
  Minus,
  Eye,
  Check,
  CheckCircle,
  HelpCircle,
  Clock,
  ChevronRight,
  Diamond,
  Target,
  MousePointerClick
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Category color mapping for trending categories
const getCategoryColor = (categoryName: string): string => {
  const colorMap: Record<string, string> = {
    'Smart Contract Platform': '#8B5CF6', // Purple
    'Layer 1 (L1)': '#06B6D4', // Cyan
    'Proof of Work (PoW)': '#F59E0B', // Amber
    'Proof of Stake (PoS)': '#10B981', // Emerald
    'Layer 2 (L2)': '#EF4444', // Red
    'Rollup': '#6366F1', // Indigo
    'World Liberty Financial Portfolio': '#EC4899', // Pink
    'DeFi': '#8B5CF6', // Purple
    'AI & Machine Learning': '#06B6D4', // Cyan
    'Gaming & Metaverse': '#EC4899', // Pink
    'Infrastructure': '#10B981', // Emerald
    'Web3 Social': '#F59E0B', // Amber
    'NFT': '#EF4444', // Red
    'Lending': '#6366F1', // Indigo
  };

  return colorMap[categoryName] || '#64748B'; // Default slate color
};
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingSectorsBarChart } from './TrendingSectorsBarChart';
import { CoinLogo } from '@/components/CoinLogo';
// import ScoreProgressCard from './ScoreProgressCard'; // Disabled - TypeScript stub only
// import PortfolioRatingCard from './PortfolioRatingCard'; // Disabled - TypeScript stub only
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// import { ScoreGauge } from '@/components/ScoreGauge'; // Disabled - TypeScript stub only

// Helper function to get score gauge status
const getScoreGaugeStatus = (score: number): 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Bad' => {
  if (score >= 90) return 'Excellent';
  if (score >= 75) return 'Good';
  if (score >= 65) return 'Fair';
  if (score >= 50) return 'Poor';
  return 'Bad';
};

// Get score status - using platform standards
const getScoreStatus = (score: number): {
  label: string;
  color: string;
  bgColor: string;
  progressColor: string;
  progressHex: string;
} => {
  if (score >= 90) {
    return {
      label: "Excellent",
      color: "text-emerald-500",
      bgColor: "bg-emerald-500/10",
      progressColor: "bg-emerald-500",
      progressHex: "#10b981"
    };
  } else if (score >= 75) {
    return {
      label: "Positive",
      color: "text-cyan-500",
      bgColor: "bg-cyan-500/10",
      progressColor: "bg-cyan-500",
      progressHex: "#06b6d4"
    };
  } else if (score >= 65) {
    return {
      label: "Average",
      color: "text-amber-500",
      bgColor: "bg-amber-500/10",
      progressColor: "bg-amber-500",
      progressHex: "#f59e0b"
    };
  } else if (score >= 50) {
    return {
      label: "Weak",
      color: "text-orange-500",
      bgColor: "bg-orange-500/10",
      progressColor: "bg-orange-500",
      progressHex: "#f97316"
    };
  } else {
    return {
      label: "Critical",
      color: "text-red-500",
      bgColor: "bg-red-500/10",
      progressColor: "bg-red-500",
      progressHex: "#ef4444"
    };
  }
};

// Create a reusable ScoreTooltip component with improved positioning
const ScoreTooltip = () => (
  <TooltipProvider>
    <UITooltip>
      <TooltipTrigger>
        <Info className="h-3 w-3 text-blue-400" />
      </TooltipTrigger>
      <TooltipContent
        className="max-w-xs"
        side="left"
        align="center"
        alignOffset={-5}
        sideOffset={5}
        avoidCollisions={true}
      >
        <p className="text-xs">These scores are calculated by CoinScout's proprietary algorithm, which dynamically rates each category based on real-time momentum, adoption trends, and performance metrics. Scores are continuously updated as market trends evolve.</p>
      </TooltipContent>
    </UITooltip>
  </TooltipProvider>
);
// Helper function for getting top assets allocation color - using blue shades for neutral visualization
const getTopAssetsAllocationColor = (top5Pct: number): string => {
  if (top5Pct < 50) return "#3b82f6";      // Standard blue for low concentration
  if (top5Pct < 65) return "#3173dc";      // Slightly darker blue for moderate concentration
  if (top5Pct < 80) return "#2563eb";      // Darker blue for high concentration
  return "#1d4ed8";                        // Darkest blue for highest concentration
};
// Define the "Rest of Portfolio" color for consistent styling
const getRestOfPortfolioColor = (): string => {
  return "#93c5fd"; // Light blue for rest of portfolio
};
// Define a function to get the color for Top 5 Assets based on concentration
const getTop5RiskColor = (percentage: number): string => {
  if (percentage < 50) return "#3b82f6"; // Standard blue for low concentration
  if (percentage < 65) return "#3173dc"; // Slightly darker blue for moderate concentration
  if (percentage < 80) return "#2563eb"; // Darker blue for high concentration
  return "#1d4ed8";                      // Darkest blue for highest concentration
};
export interface EnhancedPortfolioAllocationChartProps {
  portfolio: Portfolio;
  isLoading?: boolean;
  className?: string;
}
// Types are now defined in the shared types.ts file
// Using imported groupAssets function from portfolio-utils.ts
// Extended component-specific implementation for additional metrics
const extendedGroupAssets = (assets: PortfolioAsset[], groupBy: MetricType, portfolio?: Portfolio): ChartDataItem[] => {
  if (groupBy === 'asset') {
    // Group assets by badge types that match the AiScoreBadge component ratings
    const excellentQualityAssets = assets.filter(asset => asset.aiScore >= 85); // Excellent badge (green): 85+
    const goodQualityAssets = assets.filter(asset => asset.aiScore >= 75 && asset.aiScore < 85); // Good badge (blue): 75-84
    const mediumQualityAssets = assets.filter(asset => asset.aiScore >= 65 && asset.aiScore < 75); // Fair badge (yellow): 65-74
    const lowerQualityAssets = assets.filter(asset => asset.aiScore < 65); // Poor badge (red): <65

    // Calculate combined allocations for each quality tier
    const excellentQualityAllocation = excellentQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0);
    const goodQualityAllocation = goodQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0);
    const mediumQualityAllocation = mediumQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0);
    const lowerQualityAllocation = lowerQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0);

    // Create chart data matching the specified categories and colors
    const result = [
      {
        name: 'Excellent',
        value: excellentQualityAllocation,
        color: '#2ecc71', // Excellent (85+) - Green badge
        assets: excellentQualityAssets
      },
      {
        name: 'Good',
        value: goodQualityAllocation,
        color: '#3498db', // Good (75-84) - Blue badge
        assets: goodQualityAssets
      },
      {
        name: 'Fair',
        value: mediumQualityAllocation,
        color: '#f1c40f', // Fair badge (65-74%) - Yellow color
        assets: mediumQualityAssets
      },
      {
        name: 'Poor',
        value: lowerQualityAllocation,
        color: '#e74c3c', // Poor (<65) - Red badge
        assets: lowerQualityAssets
      }
    ];

    return result;
  }
  // "By category" tab removed as requested
  if (groupBy === 'market-cap') {
    // Use API market_cap_distribution data if available
    const apiMarketCapData = (portfolio as any)?.market_cap_distribution;

    if (apiMarketCapData && Object.keys(apiMarketCapData).length > 0) {
      // Use API data directly for market cap distribution
      console.log('📊 [Market Cap] Using API market_cap_distribution data:', apiMarketCapData);

      // Convert API data to chart format
      const result = Object.entries(apiMarketCapData).map(([categoryName, percentage]) => ({
        name: categoryName,
        value: percentage as number,
        color: MARKET_CAP_COLORS[categoryName] || '#64748b',
        assets: [], // Assets detail would need to be calculated separately if needed
        recommended: categoryName === 'Large-Cap' ? 40 : categoryName === 'Mid-Cap' ? 35 : 25,
        displayAsBarChart: true
      }));

      console.log('📊 [Market Cap] Processed API data:', result);
      return result;
    }

    // Fallback to asset-based calculation if no API data available
    console.log('📊 [Market Cap] No API data, using asset-based calculation');
    const marketCapAllocations = calculateMarketCapAllocations(assets);

    // Format the data for chart display using our utility function
    const formattedData = formatMarketCapAllocationsForChart(marketCapAllocations);

    // Add recommended values and enable bar chart display
    return formattedData.map(item => ({
      ...item,
      recommended: item.name === 'Large Cap' ? 40 : item.name === 'Mid Cap' ? 35 : 25,
      displayAsBarChart: true
    }));
  }
  if (groupBy === 'category-diversification') {
    // Use real API category_diversification data if available
    const apiCategoryData = (portfolio as any)?.category_diversification;

    if (apiCategoryData && Object.keys(apiCategoryData).length > 0) {
      // Use API data directly for category diversification
      console.log('📊 [Category Diversity] Using API category_diversification data:', apiCategoryData);

      // Define colors for different categories
      const apiSectorColors: Record<string, string> = {
        'Layer 1 (L1)': PORTFOLIO_COLORS.accent.pink,
        'Smart Contract Platform': PORTFOLIO_COLORS.accent.teal,
        'Bitcoin Ecosystem': '#F7931A', // Bitcoin orange
        'Ethereum Ecosystem': '#627EEA', // Ethereum blue
        'DeFi': PORTFOLIO_COLORS.accent.purple,
        'AI': PORTFOLIO_COLORS.accent.teal,
        'Gaming': PORTFOLIO_COLORS.accent.pink,
        'Meme': PORTFOLIO_COLORS.risk.moderate,
        'Infrastructure': PORTFOLIO_COLORS.risk.low,
        'Layer 2': PORTFOLIO_COLORS.accent.teal,
        'Stablecoin': PORTFOLIO_COLORS.risk.moderate,
        'Others': PORTFOLIO_COLORS.accent.amber
      };

      // Calculate category count for diversification score
      const nonEmptyCategories = Object.entries(apiCategoryData)
        .filter(([category, percentage]) => (percentage as number) > 0);
      const categoryCount = nonEmptyCategories.length;

      // Calculate diversification metrics
      let diversificationScore = 0;
      let diversificationRating = '';
      let diversificationFeedback = '';

      if (categoryCount >= 4) {
        diversificationScore = Math.min(90 + (categoryCount - 4) * 5, 100);
        diversificationRating = 'Excellent';
        diversificationFeedback = 'Your portfolio demonstrates excellent category diversification across multiple sectors.';
      } else if (categoryCount >= 3) {
        diversificationScore = 75 + (categoryCount - 3) * 7;
        diversificationRating = 'Good';
        diversificationFeedback = 'Your portfolio has good diversification. Consider adding more categories for enhanced stability.';
      } else if (categoryCount === 2) {
        diversificationScore = 65;
        diversificationRating = 'Average';
        diversificationFeedback = 'Your portfolio covers two categories. Expanding into additional categories can provide better risk management.';
      } else {
        diversificationScore = 35;
        diversificationRating = 'Poor';
        diversificationFeedback = 'Your portfolio lacks diversification. Consider spreading investments across multiple categories.';
      }

      // Convert API data to chart format
      const result = Object.entries(apiCategoryData).map(([categoryName, percentage]) => ({
        name: categoryName,
        value: percentage as number,
        color: apiSectorColors[categoryName] || PORTFOLIO_COLORS.accent.amber,
        assets: [] // Assets detail would need to be calculated separately if needed
      })).filter(item => item.value > 0); // Only show non-zero categories

      // Add diversification metrics to result
      (result as any).diversificationScore = diversificationScore;
      (result as any).diversificationRating = diversificationRating;
      (result as any).diversificationFeedback = diversificationFeedback;

      console.log('📊 [Category Diversity] Processed API data:', result);
      return result;
    }

    // Fallback to asset-based calculation if no API data available
    console.log('📊 [Category Diversity] No API data, using asset-based calculation');
    const sectors = {
      'DeFi': ['UNI', 'AAVE', 'COMP', 'MKR', 'SNX', 'SUSHI'],
      'AI': ['FET', 'RNDR', 'OCEAN', 'AGIX', 'NMR'],
      'Gaming': ['AXS', 'SAND', 'MANA', 'ENJ', 'YGG'],
      'Meme': ['DOGE', 'SHIB', 'PEPE'],
      'Infrastructure': ['LINK', 'FIL', 'AR', 'GRT', 'POKT'],
      'Layer 1': ['BTC', 'ETH', 'SOL', 'ADA', 'AVAX', 'DOT'],
      'Layer 2': ['MATIC', 'ARB', 'OP', 'IMX'],
      'Stablecoin': ['USDT', 'USDC', 'DAI', 'BUSD'],
      'Others': ['HNT', 'CHZ', 'BLUR', 'ICP']
    };
    const sectorColors = {
      'DeFi': PORTFOLIO_COLORS.accent.purple,
      'AI': PORTFOLIO_COLORS.accent.teal,
      'Gaming': PORTFOLIO_COLORS.accent.pink,
      'Meme': PORTFOLIO_COLORS.risk.moderate,
      'Infrastructure': PORTFOLIO_COLORS.risk.low,
      'Layer 1': PORTFOLIO_COLORS.accent.pink,
      'Layer 2': PORTFOLIO_COLORS.accent.teal,
      'Stablecoin': PORTFOLIO_COLORS.risk.moderate,
      'Others': PORTFOLIO_COLORS.accent.amber
    };
    const sectorAllocations: Record<string, number> = {};
    const assetsInSectors: Record<string, {symbol: string, allocation: number}[]> = {};

    Object.keys(sectors).forEach(sector => {
      sectorAllocations[sector] = 0;
      assetsInSectors[sector] = [];
    });

    assets.forEach(asset => {
      let sector = 'Others';
      for (const [sct, symbols] of Object.entries(sectors)) {
        if (symbols.includes(asset.symbol)) {
          sector = sct;
          break;
        }
      }
      sectorAllocations[sector] += asset.allocation;
      assetsInSectors[sector].push({
        symbol: asset.symbol,
        allocation: asset.allocation
      });
    });
    // Filter out sectors with 0 allocation
    const nonEmptySectors = Object.keys(sectorAllocations)
      .filter(sector => sectorAllocations[sector] > 0);
    // Calculate diversification score based on number of non-empty sectors
    // Excellent (5+ sectors, 90-100%)
    // Good (3-4 sectors, 75-89%)
    // Average (2 sectors, 65-74%)
    // Poor (1 sector, 0-49%)
    let diversificationScore = 0;
    let diversificationRating = '';
    let diversificationFeedback = '';
    if (nonEmptySectors.length >= 5) {
      diversificationScore = Math.min(90 + (nonEmptySectors.length - 5) * 2, 100);
      diversificationRating = 'Excellent';
      diversificationFeedback = 'Your portfolio demonstrates excellent category diversification, reducing risk and capturing diverse growth opportunities.';
    } else if (nonEmptySectors.length >= 3) {
      diversificationScore = 75 + (nonEmptySectors.length - 3) * 7;
      diversificationRating = 'Good';
      diversificationFeedback = 'Your portfolio has good diversification, but adding 1-2 more categories could further enhance stability.';
    } else if (nonEmptySectors.length === 2) {
      diversificationScore = 65;
      diversificationRating = 'Average';
      diversificationFeedback = 'Your portfolio covers two categories. Expanding into additional categories can provide better risk management.';
    } else {
      diversificationScore = Math.max(40, nonEmptySectors.length * 40);
      diversificationRating = 'Poor';
      diversificationFeedback = 'Your portfolio is too concentrated in a single category, increasing risk. Consider diversifying into other categories.';
    }
    // Sort sectors by allocation, descending
    const sortedSectorNames = Object.keys(sectorAllocations)
      .filter(sector => sectorAllocations[sector] > 0)
      .sort((a, b) => sectorAllocations[b] - sectorAllocations[a]);
    // Create the chart data
    const result = sortedSectorNames.map(sector => ({
      name: sector,
      value: sectorAllocations[sector],
      color: (sectorColors as any)[sector] || PORTFOLIO_COLORS.accent.amber,
      assets: assetsInSectors[sector].sort((a, b) => b.allocation - a.allocation), // Sort assets within sector by allocation
      sectorCount: nonEmptySectors.length,
      diversificationScore,
      diversificationRating,
      diversificationFeedback
    }));
    return result;
  }
  if (groupBy === 'trending-bar-chart') {
    // Use real API trending_categories data instead of mock data
    if (portfolio && (portfolio as any).trending_categories) {
      const trendingCategories = (portfolio as any).trending_categories;

      // Convert API trending_categories to chart format
      return trendingCategories.map((category: any, index: number) => ({
        name: category.name,
        value: category.allocation,
        color: getCategoryColor(category.name),
        assets: category.coins || [],
        coins: category.coins || [], // Include coin details for display
        displayAsHorizontalBarChart: index === 0 // Set flag for first item to trigger horizontal bar chart
      })).filter((item: any) => item.value > 0); // Only show categories with allocation
    }

    // Fallback to utility functions if API data not available
    const trendingAllocations = calculateSectorAllocations(assets);
    return formatTrendingForHorizontalBarChart(trendingAllocations);
  }
  if (groupBy === 'investment-balance') {
    // Investment balance (concentration risk) - Updated according to blueprint
    const sortedAssets = [...assets].sort((a, b) => b.allocation - a.allocation);
    // Get top 5 assets
    const top5Assets = sortedAssets.slice(0, 5);
    // Calculate combined percentage of top 5 assets
    const top5Combined = top5Assets.reduce((sum, asset) => sum + asset.allocation, 0);
    // Rest of portfolio
    const otherHoldings = 100 - top5Combined;
    // Using our globally defined color functions for consistency
    return [
      {
        name: 'Top 5 Assets',
        value: top5Combined,
        color: getTop5RiskColor(top5Combined),
        top5Assets: top5Assets.map(asset => ({
          symbol: asset.symbol,
          name: asset.name || asset.symbol,
          allocation: asset.allocation
        }))
      },
      {
        name: 'Rest of Portfolio',
        value: otherHoldings,
        color: getRestOfPortfolioColor()
      }
    ];
  }
  if (groupBy === 'stablecoin-ratio') {
    // Use real API data from stablecoin_ratio field
    const stablecoinData = (portfolio as any).stablecoin_ratio?.stablecoin_analysis;
    if (!stablecoinData) {
      // Fallback if no API data
      return [];
    }

    // Extract real API values
    const currentRatio = stablecoinData.current_ratio || 0;
    const currentValue = stablecoinData.current_value || 0;
    const targetRatio = stablecoinData.target_ratio || 20;
    const targetValue = stablecoinData.target_value || 0;
    const recommendedRange = `${stablecoinData.recommended_range?.min || 20}%-${stablecoinData.recommended_range?.max || 25}%`;
    const status = stablecoinData.status || 'unknown';

    // Return special format for stablecoin ratio view with real API data
    return [{
      name: 'Stablecoin Ratio',
      value: currentRatio,
      color: PORTFOLIO_COLORS.chart.portfolio, // Green/teal
      portfolioValue: portfolio.totalValue || 0,
      currentValue: Math.round(currentValue * 100) / 100,
      targetValue: Math.round(targetValue * 100) / 100,
      recommendedRange: recommendedRange,
      currentRatio: Math.round(currentRatio * 10) / 10,
      targetRatio: targetRatio,
      status: status,
      displayAsSpecialRatio: true // Marker for special rendering
    }];
  }
  // Default empty case
  return [];
};
// Using utility functions imported from portfolio-utils.ts
// Note: These utility functions provide consistent behavior across components
// Using imported CategoryBreakdown component from CategoryBreakdown.tsx
// Using the calculateCoinScoutScore function from portfolio-utils.ts
export function EnhancedPortfolioAllocationChart({
  portfolio: ignoredPortfolio,
  isLoading = false,
  className
}: EnhancedPortfolioAllocationChartProps) {
  // Use real portfolio data directly
  const portfolio = ignoredPortfolio;

  const [selectedMetric, setSelectedMetric] = React.useState<'overall-portfolio' | 'asset' | 'market-cap' | 'category-diversification' | 'trending-bar-chart' | 'investment-balance' | 'stablecoin-ratio' | 'coinscore'>('coinscore');
  const [showMethodologyModal, setShowMethodologyModal] = React.useState<string | null>(null);
  // Comprehensive methodology modal data for all metrics
  const methodologyData: Record<string, {
    score: number;
    icon: any;
    description: string;
    importance: string[];
    scoringLevels: { range: string; status: string; color: string }[];
    weightingTable?: { metric: string; score: number; weight: string; contribution: number }[];
  }> = {
    'Overall Portfolio': {
      score: 89,
      icon: Briefcase,
      description: 'We calculate your overall portfolio score by evaluating and weighting six key metrics that determine portfolio health, risk management, and growth potential. This comprehensive score reflects your portfolio\'s overall strength.',
      importance: [
        'Comprehensive Assessment: Combines multiple factors for holistic portfolio evaluation',
        'Risk-Adjusted Performance: Balances growth potential with stability metrics',
        'Actionable Insights: Identifies specific areas for portfolio improvement'
      ],
      scoringLevels: [
        { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
        { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
        { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
        { range: '50% - 64%', status: 'Caution', color: '#fb923c' },
        { range: 'Below 50%', status: 'Risky', color: '#ef4444' }
      ],
      weightingTable: [
        { metric: 'Portfolio Quality', score: 87, weight: '20%', contribution: 17.4 },
        { metric: 'Market Cap', score: 89, weight: '15%', contribution: 13.4 },
        { metric: 'Trend Category', score: 92, weight: '20%', contribution: 18.4 },
        { metric: 'Category Diversity', score: 85, weight: '15%', contribution: 12.8 },
        { metric: 'Portfolio Weight', score: 90, weight: '15%', contribution: 13.5 },
        { metric: 'Stablecoin Ratio', score: 92, weight: '15%', contribution: 13.8 }
      ]
    },
    'Portfolio Quality': {
      score: 87,
      icon: ChartBar,
      description: 'We evaluate the fundamental quality and CoinScout scores of all projects in your portfolio. This includes team credentials, technology innovation, market adoption, and overall project health indicators.',
      importance: [
        'Quality Projects: High quality projects with strong fundamentals tend to survive market downturns better.',
        'Long-term Performance: Projects with solid teams and technology typically deliver better long-term returns.',
        'Risk Mitigation: Avoiding low-quality projects reduces the risk of significant losses from project failures.'
      ],
      scoringLevels: [
        { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
        { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
        { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
        { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
      ]
    },
    'Market Cap': {
      score: 89,
      icon: Coins,
      description: 'Analysis of market capitalization distribution across your portfolio, balancing large-cap stability with small-cap growth potential.',
      importance: [
        'Risk Management: Large-cap assets provide stability during market volatility.',
        'Growth Potential: Small and mid-cap assets offer higher growth opportunities.',
        'Balanced Exposure: Proper distribution across market caps optimizes risk-reward ratio.'
      ],
      scoringLevels: [
        { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
        { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
        { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
        { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
      ]
    },
    'Trend Category': {
      score: 76,
      icon: TrendingUp,
      description: 'Tracking sectors which provides both growth potential from emerging technologies and stability from established assets. This balanced approach helps reduce overall market vulnerability.',
      importance: [
        'Market Alpha: Trending sectors often outperform the broader market.',
        'Innovation Exposure: Early positioning in emerging technologies can yield significant returns.',
        'Momentum Trading: Trending categories attract more investment and attention.'
      ],
      scoringLevels: [
        { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
        { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
        { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
        { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
      ]
    },
    'Category Diversity': {
      score: 85,
      icon: BarChart3,
      description: 'Measuring the breadth of sectors in your portfolio. Higher diversity reduces concentration risk and captures opportunities across the crypto ecosystem.',
      importance: [
        'Risk Distribution: Diverse sectors protect against sector-specific downturns.',
        'Opportunity Capture: Exposure to multiple sectors increases chances of catching breakout trends.',
        'Portfolio Stability: Diversification reduces overall portfolio volatility.'
      ],
      scoringLevels: [
        { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
        { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
        { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
        { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
      ]
    },
    'Portfolio Weight': {
      score: 68,
      icon: Scale,
      description: 'Evaluating asset allocation balance to prevent overconcentration in any single asset, ensuring no position poses excessive risk.',
      importance: [
        'Concentration Risk: Overweighted positions can lead to significant losses.',
        'Rebalancing Efficiency: Proper weights allow for easier portfolio management.',
        'Risk-Adjusted Returns: Optimal weighting maximizes returns for given risk level.'
      ],
      scoringLevels: [
        { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
        { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
        { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
        { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
      ]
    },
    'By Stablecoin Ratio': {
      score: 92,
      icon: DollarSign,
      description: 'Assessing the percentage of stablecoins in your portfolio as a risk management tool and liquidity buffer.',
      importance: [
        'Risk Buffer: Stablecoins provide safety during market downturns.',
        'Buying Power: Maintains liquidity for opportunistic purchases.',
        'Portfolio Stability: Reduces overall portfolio volatility.'
      ],
      scoringLevels: [
        { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
        { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
        { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
        { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
      ]
    }
  };

  // Define the available metrics tabs - RESTORED TO ORIGINAL FROM 48 HOURS AGO
  const metricTabs: MetricTab[] = [
    {
      id: 'coinscore',
      name: 'Overall portfolio',
      icon: <Award className="h-4 w-4" />,
      description: 'Weighted average CoinScout rating of the portfolio for risk assessment'
    },
    {
      id: 'trending-bar-chart',
      name: 'Trend Category',
      icon: <TrendingUp className="h-4 w-4" />,
      description: 'Shows your exposure to trending cryptocurrency categories for optimal growth potential'
    },
    {
      id: 'category-diversification',
      name: 'Category Diversity',
      icon: <BarChart2 className="h-4 w-4" />,
      description: 'Evaluates portfolio sector diversification across DeFi, AI, GameFi, Metaverse (Coefficient: 0.15)'
    },
    {
      id: 'market-cap',
      name: 'Market Cap',
      icon: <CircleDollarSign className="h-4 w-4" />,
      description: 'Distribution across Large-Cap, Mid-Cap, and Small-Cap tokens'
    },
    {
      id: 'investment-balance',
      name: 'Portfolio Weight',
      icon: <Scale className="h-4 w-4" />,
      description: 'Evaluates how evenly distributed your portfolio is across assets to identify concentration risk'
    },
    {
      id: 'asset',
      name: 'Portfolio Quality',
      icon: <Award className="h-4 w-4" />,
      description: 'Analysis of portfolio assets based on CoinScout Project Quality Scores'
    },
    {
      id: 'stablecoin-ratio',
      name: 'By Stablecoin Ratio',
      icon: <DollarSign className="h-4 w-4" />,
      description: 'Proportion of stablecoins for liquidity and risk mitigation'
    }
  ];
  // Use our extended implementation for advanced metrics and fallback to the utility function for basic metrics
  // For "overall-portfolio", we'll render a special component so we don't need chartData
  const chartData =
    selectedMetric !== 'overall-portfolio' ? (
      selectedMetric === 'market-cap' ||
      selectedMetric === 'category-diversification' ||
      selectedMetric === 'trending-bar-chart' ||
      selectedMetric === 'investment-balance' ||
      selectedMetric === 'stablecoin-ratio' ||
      selectedMetric === 'asset'
        ? extendedGroupAssets(portfolio.assets || [], selectedMetric, portfolio)
        : groupAssets(portfolio.assets || [], selectedMetric)
    ) : [];
  const coinScoutScore = calculateCoinScoutScore(portfolio.assets || []);
  // Extract category-specific metrics if we're on the category view
  let categoryScoreData = {};
  if (selectedMetric === 'category-diversification' && chartData.length > 0) {
    categoryScoreData = {
      categoryCount: chartData.length,
      diversificationScore: 75, // Default score for "Good" rating with 3-4 sectors
      diversificationRating: 'Good',
      diversificationFeedback: 'Your portfolio has good diversification, but adding 1-2 more categories could further enhance stability.'
    };
  }
  if (isLoading) {
    return (
      <Card className={cn("bg-slate-800/60 border-slate-700/20 overflow-hidden", className)}>
        <div className="flex flex-col md:flex-row h-full w-full min-h-[500px]">
          {/* Loading sidebar */}
          <div className="w-full md:w-[250px] bg-slate-800/80 border-r border-slate-700/20 p-4">
            <Skeleton className="h-6 w-[120px] bg-slate-700/50 mb-4" />
            <div className="space-y-3">
              {[1, 2, 3, 4, 5, 6, 7, 8].map(i => (
                <Skeleton key={i} className="h-10 w-full bg-slate-700/50" />
              ))}
            </div>
          </div>
          {/* Loading chart area */}
          <div className="flex-1 p-5 flex flex-col items-center justify-center">
            <Skeleton className="h-[300px] w-[300px] bg-slate-700/50 rounded-full mx-auto my-4" />
            <Skeleton className="h-6 w-[200px] bg-slate-700/50 mt-4" />
          </div>
        </div>
      </Card>
    );
  }
  // Determine score and trend status based on selected metric
  const getScoreAndStatus = () => {
    if (selectedMetric === 'overall-portfolio') {
      // API verilerinden gerçek skorları al, yoksa loading göster
      const diversificationScore = portfolio.metrics?.category_diversification?.score ?? null;
      const marketCapScore = portfolio.metrics?.marketCapDistribution?.score ?? null;
      // Use trending_categories data for trend score calculation
      const trendingCategories = (portfolio as any).trending_categories;
      const trendingScore = trendingCategories && trendingCategories.length > 0 ?
        Math.min(Math.round(trendingCategories.reduce((sum: number, cat: any) => sum + cat.allocation, 0) * 2), 100) : 75; // Default fallback value
      // Use market_cap_distribution for portfolio weight calculation
      const marketCapDist = (portfolio as any).market_cap_distribution;
      const investmentBalanceScore = marketCapDist
        ? Object.values(marketCapDist).reduce((sum: number, val: any) => sum + (typeof val === 'number' ? val : 0), 0)
        : 80; // Default fallback value instead of null
      const coinQualityScore = portfolio.metrics?.portfolio_quality?.score ?? null;

      // All metrics now have fallback values, so no need for loading check
      // Calculate weighted average (weights should sum to 1)
      const weightedScore = (
        (diversificationScore * 0.25) +  // 25% weight to diversification
        (marketCapScore * 0.20) +        // 20% weight to market cap distribution
        (trendingScore * 0.15) +         // 15% weight to trend following
        (investmentBalanceScore * 0.15) + // 15% weight to investment balance
        (coinQualityScore * 0.25)        // 25% weight to coin quality
      );
      // Round to nearest integer
      const finalScore = Math.round(weightedScore);
      // Determine status based on score
      let status = '';
      if (finalScore >= 85) {
        status = 'Excellent';
      } else if (finalScore >= 75) {
        status = 'Very Good';
      } else if (finalScore >= 65) {
        status = 'Good';
      } else if (finalScore >= 50) {
        status = 'Average';
      } else {
        status = 'Needs Attention';
      }
      return {
        score: finalScore,
        status: status,
        summaryText: 'Your overall portfolio health score reflects a weighted analysis of diversification, market cap distribution, trend exposure, investment balance, and individual asset quality.'
      };
    } else if (selectedMetric === 'category-diversification') {
      // Calculate sector count
      const sectorCount = chartData.length > 0 ? (chartData.length) : 3;
      // Determine score based on sector count as per blueprint
      let score = 75;
      let status = 'Good';
      let summaryText = '';
      if (sectorCount >= 5) {
        score = 95;
        status = 'Excellent';
        summaryText = 'Your portfolio demonstrates excellent sector diversification, encompassing five or more distinct sectors. This broad exposure reduces risk and captures diverse growth opportunities.';
      } else if (sectorCount >= 3 && sectorCount <= 4) {
        score = 85;
        status = 'Good';
        summaryText = 'Your portfolio is good in sector diversification, including three to four sectors. This provides a balanced risk spread while still allowing for targeted investment in key areas.';
      } else if (sectorCount === 2) {
        score = 70;
        status = 'Average';
        summaryText = 'Your portfolio has average sector diversification, with investments spread across two sectors. While some risk is mitigated, increasing sector variety could enhance stability and growth potential.';
      } else {
        score = 45;
        status = 'Poor';
        summaryText = 'Your portfolio exhibits poor sector diversification, focusing on a single sector. This concentration heightens risk and limits exposure to broader market opportunities.';
      }
      return {
        score,
        status: `${status} (${sectorCount} sectors)`,
        summaryText
      };
    } else if (selectedMetric === 'trending-bar-chart') {
      // Use trending_categories data from API
      const trendingCategories = (portfolio as any).trending_categories;
      if (!trendingCategories || trendingCategories.length === 0) {
        return {
          score: null,
          status: 'Loading...',
          summaryText: 'Calculating trending sector allocation...'
        };
      }
      // Calculate trend score based on trending categories allocation
      const totalTrendingAllocation = trendingCategories.reduce((sum: number, cat: any) => sum + cat.allocation, 0);
      const trendScore = Math.min(Math.round(totalTrendingAllocation * 2), 100); // Scale to 0-100
      return {
        score: trendScore,
        status: trendScore >= 75 ? 'Well Balanced' : trendScore >= 50 ? 'Moderately Balanced' : 'Needs Improvement',
        summaryText: `Your portfolio has ${totalTrendingAllocation.toFixed(1)}% allocation to trending sectors, providing both growth potential and stability.`
      };
    } else if (selectedMetric === 'market-cap') {
      // API'den gelen alan ismi: marketCapDistribution
      const marketCapScore = portfolio.metrics?.marketCapDistribution?.score;
      if (marketCapScore === null || marketCapScore === undefined) {
        return {
          score: null,
          status: 'Loading...',
          summaryText: 'Calculating market cap distribution...'
        };
      }
      return {
        score: Math.round(marketCapScore),
        status: 'Moderately Balanced',
        summaryText: 'Your portfolio distribution across market cap categories.'
      };
    } else if (selectedMetric === 'coinscore') {
      const overallScore = portfolio.overallScore;
      if (overallScore === null || overallScore === undefined) {
        return {
          score: null,
          status: 'Loading...',
          summaryText: 'Calculating your CoinScout Score...'
        };
      }
      return {
        score: Math.round(overallScore * 10) / 10,
        status: overallScore >= 75 ? 'Positive' : overallScore >= 65 ? 'Average' : 'Weak',
        summaryText: 'Your CoinScout Score indicates the overall health and optimization of your cryptocurrency portfolio.'
      };
    } else if (selectedMetric === 'investment-balance') {
      // Use market_cap_distribution for portfolio weight calculation
      const marketCapDist = (portfolio as any).market_cap_distribution;
      const balanceScore = marketCapDist
        ? Object.values(marketCapDist).reduce((sum: number, val: any) => sum + (typeof val === 'number' ? val : 0), 0)
        : 80; // Default fallback value instead of null
      // balanceScore now has fallback value, no need for loading check
      return {
        score: Math.round(balanceScore),
        status: balanceScore >= 75 ? 'Well Balanced' : balanceScore >= 50 ? 'Moderately Balanced' : 'Needs Rebalancing',
        summaryText: 'Your investment balance shows the distribution across your portfolio assets.'
      };
    } else {
      // Diğer metrikler için de API verilerini kontrol et
      const relevantScore = selectedMetric === 'asset' ? portfolio.metrics?.portfolio_quality?.score :
                           selectedMetric === 'stablecoin-ratio' ? portfolio.metrics?.stablecoinRatio?.score :
                           selectedMetric === 'category-diversification' ? portfolio.metrics?.category_diversification?.score :
                           null;

      if (relevantScore === null || relevantScore === undefined) {
        return {
          score: null,
          status: 'Loading...',
          summaryText: 'Calculating metric data...'
        };
      }

      return {
        score: Math.round(relevantScore),
        status: 'Balanced',
        summaryText: 'Your portfolio maintains a balanced approach with a healthy mix of assets across various categories.'
      };
    }
  };
  const { score, status, summaryText } = getScoreAndStatus();
  // Extract score card elements based on the selected metric
  const getScoreCardContent = () => {
    if (selectedMetric === 'trending-bar-chart') {
      // Use trending_categories data for trend score calculation
      const trendingCategories = (portfolio as any).trending_categories;
      const trendScore = trendingCategories && trendingCategories.length > 0 ?
        Math.min(Math.round(trendingCategories.reduce((sum: number, cat: any) => sum + cat.allocation, 0) * 2), 100) : 75;
      const trendStatus = getScoreStatus(trendScore);

      return (
        <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 lg:p-5 h-full flex flex-col shadow-lg">
          {/* Metric Card - matching dashboard design */}
          <Card
            className="bg-slate-800/90 border-slate-700/40 hover:bg-slate-800/80 transition-all cursor-pointer
                     shadow-md shadow-slate-900/40 hover:shadow-lg hover:shadow-slate-900/50
                     transform hover:-translate-y-1 group mb-5"
            onClick={() => setShowMethodologyModal('Trend Category')}
          >
            <CardContent className="p-5">
              <div className="flex items-center gap-2 mb-3">
                <div className={cn("p-1.5 rounded-md", trendStatus.bgColor)}>
                  <TrendingUp className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-white">Trend Category</h4>
                </div>
                <ChevronRight className="h-4 w-4 text-slate-400 transition-colors group-hover:text-slate-200" />
              </div>

              <div className="space-y-2">
                <div className="flex items-baseline justify-between">
                  <span className={cn("text-2xl font-bold", trendStatus.color)}>
                    {trendScore}
                  </span>
                  <span className={cn("text-xs", trendStatus.color)}>
                    {trendStatus.label}
                  </span>
                </div>

                <Progress
                  key={`trend-category-${trendScore}`}
                  value={trendScore}
                  className="h-1.5 bg-slate-700/50"
                />
              </div>
            </CardContent>
          </Card>

          {/* Summary Section */}
          <div className="mb-5">
            <h4 className="text-xs font-medium text-slate-300 mb-2 flex items-center gap-2">
              <BarChart2 className="h-3.5 w-3.5 text-blue-400" aria-hidden="true" />
              <span>Summary</span>
            </h4>
            <div className="bg-slate-800/40 rounded-md p-3 border border-slate-700/30 shadow-md">
              <p className="text-xs text-slate-300 leading-relaxed">
                Your portfolio has a balanced 40% allocation to trending sectors, which provides both
                growth potential from emerging technologies and stability from established assets.
                This balanced approach helps reduce overall market vulnerability.
              </p>
            </div>
          </div>
          {/* Recommendations Section - Trending Bar Chart Fallback */}
          <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 mb-5">
            <h4 className="text-sm font-medium text-white mb-3">Recommendations</h4>
            <div className="space-y-2 text-sm text-slate-300">
              <div className="flex items-start gap-2">
                <span className="text-xs">✓ Maintain current allocation ratio for optimal risk-reward balance.</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">• Consider investing in trending sectors to improve growth potential.</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">ℹ Monitor trending tech sectors for optimal exposure.</span>
              </div>
            </div>
          </div>
          {/* Trending Categories Section - Real API Data */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-white mb-3">Trending Categories Analysis</h4>
            {(portfolio as any).trending_categories && (
              <div className="space-y-2">
                {(portfolio as any).trending_categories
                  .filter((category: any) => category.allocation > 0)
                  .sort((a: any, b: any) => b.allocation - a.allocation)
                  .slice(0, 10) // Top 10 categories
                  .map((category: any, index: number) => (
                    <div key={category.name} className="flex justify-between items-center bg-slate-800/80 rounded-md p-2 border border-slate-700/50 shadow-md">
                      <div className="flex items-center gap-2">
                        <div className="text-xs font-medium text-white">{index + 1}</div>
                        <div className="text-xs text-white">{category.name}</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-slate-300">{category.allocation.toFixed(1)}%</span>
                        <Badge
                          className="text-xs"
                          style={{
                            backgroundColor: getCategoryColor(category.name) + '20',
                            color: getCategoryColor(category.name),
                            border: `1px solid ${getCategoryColor(category.name)}40`
                          }}
                        >
                          {category.coins?.length || 0} coins
                        </Badge>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>
          {/* Removed duplicate Allocation Score card */}
        </div>
      );
    } else if (selectedMetric === 'market-cap') {
      // Calculate market cap score from portfolio metrics
      const marketCapScore = Math.round((portfolio.metrics?.marketCapDistribution?.score ?? 89));
      const marketCapStatus = getScoreStatus(marketCapScore);

      return (
        <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 lg:p-5 h-full flex flex-col shadow-lg">
          {/* Market Cap Score Card - matching dashboard design */}
          <Card
            className="bg-slate-800/90 border-slate-700/40 hover:bg-slate-800/80 transition-all cursor-pointer
                     shadow-md shadow-slate-900/40 hover:shadow-lg hover:shadow-slate-900/50
                     transform hover:-translate-y-1 group mb-5"
            onClick={() => setShowMethodologyModal('Market Cap')}
          >
            <CardContent className="p-5">
              <div className="flex items-center gap-2 mb-3">
                <div className={cn("p-1.5 rounded-md", marketCapStatus.bgColor)}>
                  <BarChart2 className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-white">Market Cap</h4>
                </div>
                <ChevronRight className="h-4 w-4 text-slate-400 transition-colors group-hover:text-slate-200" />
              </div>

              <div className="space-y-2">
                <div className="flex items-baseline justify-between">
                  <span className={cn("text-2xl font-bold", marketCapStatus.color)}>
                    {marketCapScore}
                  </span>
                  <span className={cn("text-xs", marketCapStatus.color)}>
                    {marketCapStatus.label}
                  </span>
                </div>

                <Progress
                  key={`market-cap-${marketCapScore}`}
                  value={marketCapScore}
                  className="h-1.5 bg-slate-700/50"
                />
              </div>
            </CardContent>
          </Card>
          {/* Summary Section */}
          <div className="mb-5">
            <h4 className="text-xs font-medium text-slate-300 mb-2 flex items-center gap-2">
              <BarChart2 className="h-3.5 w-3.5 text-blue-400" aria-hidden="true" />
              <span>Summary</span>
            </h4>
            <div className="bg-slate-800/40 rounded-md p-3 border border-slate-700/30 shadow-md">
              <p className="text-xs text-slate-300 leading-relaxed">
                Your portfolio leans toward mid-cap assets (51%) with less exposure to large-caps (28%) than
                typically recommended (40%). This creates moderate growth potential with slightly higher risk.
              </p>
            </div>
          </div>
          {/* Recommendations Section - Market Cap Fallback */}
          <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 mb-5">
            <h4 className="text-sm font-medium text-white mb-3">Recommendations</h4>
            <div className="space-y-2 text-sm text-slate-300">
              <div className="flex items-start gap-2">
                <span className="text-xs">• Increase large-cap exposure by 12% for better stability</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">⚠ Reduce small-cap exposure by 8% to limit volatility</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">✓ Current mid-cap allocation is well-positioned</span>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (selectedMetric === 'portfolio-quality') {
      // Overall portfolio tab with main score and 6 metric cards
      const mainScore = Math.round((portfolio.overallScore || 89) * 10) / 10;
      const mainStatus = 'Positive';
      const mainStatusColor = '#00E5E5'; // Cyan color for Positive status

      const metrics = [
        {
          name: 'Portfolio Quality',
          score: Math.round((portfolio.metrics?.portfolio_quality?.score ?? 87) * 10) / 10,
          icon: ChartBar,
          description: 'We evaluate the fundamental quality and CoinScout scores of all projects in your portfolio. This includes team credentials, technology innovation, market adoption, and overall project health indicators.',
          importance: [
            'Quality Projects: High quality projects with strong fundamentals tend to survive market downturns better.',
            'Long-term Performance: Projects with solid teams and technology typically deliver better long-term returns.',
            'Risk Mitigation: Avoiding low-quality projects reduces the risk of significant losses from project failures.'
          ],
          scoringLevels: [
            { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
            { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
            { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
            { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
          ]
        },
        {
          name: 'Market Cap',
          score: Math.round((portfolio.metrics?.marketCapDistribution?.score ?? 89) * 10) / 10,
          icon: Coins,
          description: 'Analysis of market capitalization distribution across your portfolio, balancing large-cap stability with small-cap growth potential.',
          importance: [
            'Risk Management: Large-cap assets provide stability during market volatility.',
            'Growth Potential: Small and mid-cap assets offer higher growth opportunities.',
            'Balanced Exposure: Proper distribution across market caps optimizes risk-reward ratio.'
          ],
          scoringLevels: [
            { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
            { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
            { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
            { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
          ]
        },
        {
          name: 'Trend Category',
          score: Math.round((portfolio.metrics?.trend_category?.score ?? 92) * 10) / 10,
          icon: TrendingUp,
          description: 'Tracking sectors which provides both growth potential from emerging technologies and stability from established assets. This balanced approach helps reduce overall market vulnerability.',
          importance: [
            'Market Alpha: Trending sectors often outperform the broader market.',
            'Innovation Exposure: Early positioning in emerging technologies can yield significant returns.',
            'Momentum Trading: Trending categories attract more investment and attention.'
          ],
          scoringLevels: [
            { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
            { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
            { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
            { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
          ]
        },
        {
          name: 'Category Diversity',
          score: Math.round((portfolio.metrics?.category_diversification?.score ?? 85) * 10) / 10,
          icon: BarChart3,
          description: 'Measuring the breadth of sectors in your portfolio. Higher diversity reduces concentration risk and captures opportunities across the crypto ecosystem.',
          importance: [
            'Risk Distribution: Diverse sectors protect against sector-specific downturns.',
            'Opportunity Capture: Exposure to multiple sectors increases chances of catching breakout trends.',
            'Portfolio Stability: Diversification reduces overall portfolio volatility.'
          ],
          scoringLevels: [
            { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
            { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
            { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
            { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
          ]
        },
        {
          name: 'Portfolio Weight',
          score: (() => {
            const marketCapDist = (portfolio as any).market_cap_distribution;
            return marketCapDist
              ? Math.round(Object.values(marketCapDist).reduce((sum: number, val: any) => sum + (typeof val === 'number' ? val : 0), 0) * 10) / 10
              : 90;
          })(),
          icon: Scale,
          description: 'Evaluating asset allocation balance to prevent overconcentration in any single asset, ensuring no position poses excessive risk.',
          importance: [
            'Concentration Risk: Overweighted positions can lead to significant losses.',
            'Rebalancing Efficiency: Proper weights allow for easier portfolio management.',
            'Risk-Adjusted Returns: Optimal weighting maximizes returns for given risk level.'
          ],
          scoringLevels: [
            { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
            { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
            { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
            { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
          ]
        },
        {
          name: 'Stablecoin Ratio',
          score: Math.round((portfolio.metrics?.stablecoinRatio?.score ?? 92) * 10) / 10,
          icon: DollarSign,
          description: 'Assessing the percentage of stablecoins in your portfolio as a risk management tool and liquidity buffer.',
          importance: [
            'Risk Buffer: Stablecoins provide safety during market downturns.',
            'Buying Power: Maintains liquidity for opportunistic purchases.',
            'Portfolio Stability: Reduces overall portfolio volatility.'
          ],
          scoringLevels: [
            { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
            { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
            { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
            { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
          ]
        }
      ];

      return (
        <div className="h-full flex flex-col">
          {/* Main Score Display */}
          <div className="text-center mb-6">
            <div className="flex items-baseline justify-center gap-2 mb-2">
              <span className="text-7xl font-bold" style={{ color: mainStatusColor }}>
                {mainScore}
              </span>
              <span className="text-xl text-slate-400">out of 100</span>
            </div>
            <div className="text-lg font-medium" style={{ color: mainStatusColor }}>
              {mainStatus}
            </div>

            {/* Progress Bar */}
            <div className="w-full mt-4 mb-4">
              <div className="h-2 bg-slate-700/50 rounded-full overflow-hidden">
                <div
                  className="h-full rounded-full animate-grow-width"
                  style={{
                    backgroundColor: mainStatusColor,
                    '--target-width': `${mainScore}%`,
                    animationDelay: '0.2s'
                  } as React.CSSProperties}
                />
              </div>
            </div>

            {/* Info Text */}
            <div className="flex items-center justify-center gap-2 text-sm text-slate-300">
              <Info className="h-4 w-4" />
              <span>Review metrics below 75% for improvement opportunities.</span>
            </div>
          </div>

          {/* Metric Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 flex-1">
            {metrics.map((metric, index) => {
              const Icon = metric.icon;
              const metricScore = metric.score ?? 0;
              const status = metricScore >= 90 ? 'Excellent' :
                           metricScore >= 75 ? 'Positive' :
                           metricScore >= 65 ? 'Average' : 'Weak';
              const statusColor = metricScore >= 90 ? '#22c55e' :
                                metricScore >= 75 ? '#00E5E5' :
                                metricScore >= 65 ? '#fbbf24' : '#ef4444';

              return (
                <Card
                  key={index}
                  className="bg-slate-800/90 border-slate-700/40 hover:bg-slate-800/80 transition-all cursor-pointer
                           shadow-md shadow-slate-900/40 hover:shadow-lg hover:shadow-slate-900/50
                           transform hover:-translate-y-1 group"
                  onClick={() => setShowMethodologyModal(metric.name)}
                >
                  <CardContent className="p-5 h-full flex flex-col">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="p-1.5 rounded-md bg-slate-700/50">
                        <Icon className="h-4 w-4 text-slate-300" />
                      </div>
                      <h4 className="text-sm font-medium text-white flex-1">{metric.name}</h4>
                      <ChevronRight className="h-4 w-4 text-slate-400 transition-colors group-hover:text-slate-200" />
                    </div>

                    <div className="mt-auto">
                      <div className="flex items-baseline justify-between mb-2">
                        <span className="text-3xl font-bold" style={{ color: statusColor }}>
                          {metricScore}
                        </span>
                        <span className="text-sm" style={{ color: statusColor }}>
                          {status}
                        </span>
                      </div>

                      <Progress
                        value={metricScore}
                        className="h-1.5 bg-slate-700/50"
                      />
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Methodology Modal */}
          {showMethodologyModal && (
            <Dialog open={!!showMethodologyModal} onOpenChange={() => setShowMethodologyModal(null)}>
              <DialogContent className="bg-slate-900/95 border-slate-700/40 text-white max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="text-xl font-semibold flex items-center gap-3">
                    {(() => {
                      const metric = metrics.find(m => m.name === showMethodologyModal);
                      if (!metric) return null;
                      const Icon = metric.icon;
                      const metricScore = metric.score ?? 0;
                      const status = metricScore >= 90 ? 'Excellent' :
                                   metricScore >= 75 ? 'Positive' :
                                   metricScore >= 65 ? 'Average' : 'Weak';
                      const statusColor = metricScore >= 90 ? '#22c55e' :
                                        metricScore >= 75 ? '#00E5E5' :
                                        metricScore >= 65 ? '#fbbf24' : '#ef4444';

                      return (
                        <>
                          <Icon className="h-5 w-5" />
                          <span>{metric.name}</span>
                          <Badge
                            className="ml-auto"
                            style={{
                              backgroundColor: `${statusColor}20`,
                              color: statusColor,
                              borderColor: statusColor
                            }}
                          >
                            {status}
                          </Badge>
                        </>
                      );
                    })()}
                  </DialogTitle>
                </DialogHeader>

                {(() => {
                  const metric = metrics.find(m => m.name === showMethodologyModal);
                  if (!metric) return null;
                  const metricScore = metric.score ?? 0;

                  return (
                    <div className="space-y-6 mt-6">
                      {/* Score Display */}
                      <div className="text-center py-4 bg-slate-800/50 rounded-lg">
                        <div className="text-3xl font-bold mb-1" style={{
                          color: metricScore >= 90 ? '#22c55e' :
                                metricScore >= 75 ? '#00E5E5' :
                                metricScore >= 65 ? '#fbbf24' : '#ef4444'
                        }}>
                          {metricScore} / 100
                        </div>
                        <div className="text-sm text-slate-400">{metric.name} SCORE</div>
                      </div>

                      {/* What Are We Scoring */}
                      <div>
                        <h3 className="text-base font-semibold mb-3 flex items-center gap-2">
                          <Target className="h-4 w-4 text-blue-400" />
                          What Are We Scoring?
                        </h3>
                        <p className="text-sm text-slate-300 leading-relaxed">
                          {metric.description}
                        </p>
                      </div>

                      {/* Why Is This Important */}
                      <div>
                        <h3 className="text-base font-semibold mb-3 flex items-center gap-2">
                          <Info className="h-4 w-4 text-yellow-400" />
                          Why Is This Important?
                        </h3>
                        <ul className="space-y-2">
                          {metric.importance.map((item, idx) => (
                            <li key={idx} className="flex items-start gap-2 text-sm text-slate-300">
                              <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                              <span>{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Scoring Levels */}
                      <div>
                        <h3 className="text-base font-semibold mb-3 flex items-center gap-2">
                          <BarChart3 className="h-4 w-4 text-purple-400" />
                          Scoring Levels
                        </h3>
                        <div className="space-y-2">
                          {metric.scoringLevels.map((level, idx) => (
                            <div key={idx} className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
                              <div className="flex items-center gap-3">
                                <div
                                  className="w-3 h-3 rounded-full"
                                  style={{ backgroundColor: level.color }}
                                />
                                <span className="text-sm font-medium" style={{ color: level.color }}>
                                  {level.status}
                                </span>
                              </div>
                              <span className="text-sm text-slate-400">{level.range}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </DialogContent>
            </Dialog>
          )}
        </div>
      );
    } else if (selectedMetric === 'category-diversification') {
      // Calculate sector count based on the actual chart data length
      const sectorCount = chartData.length > 0 ? chartData.length : 3;
      // Determine diversification score based on sector count as per blueprint
      let score = 75; // Default "Good" score
      let rating = 'Good';
      if (sectorCount >= 5) {
        score = 95; // Excellent
        rating = 'Excellent';
      } else if (sectorCount >= 3 && sectorCount <= 4) {
        score = 85; // Good
        rating = 'Good';
      } else if (sectorCount === 2) {
        score = 70; // Average
        rating = 'Average';
      } else {
        score = 45; // Poor
        rating = 'Poor';
      }
      // Get description based on sector count
      let description = '';
      if (sectorCount >= 5) {
        description = 'Your portfolio demonstrates excellent sector diversification, encompassing five or more distinct sectors. This broad exposure reduces risk and captures diverse growth opportunities within the crypto market.';
      } else if (sectorCount >= 3 && sectorCount <= 4) {
        description = 'Your portfolio is good in sector diversification, including three to four sectors. This provides a balanced risk spread while still allowing for targeted investment in key areas.';
      } else if (sectorCount === 2) {
        description = 'Your portfolio has average sector diversification, with investments spread across two sectors. While some risk is mitigated, increasing sector variety could enhance stability and growth potential.';
      } else {
        description = 'Your portfolio exhibits poor sector diversification, focusing on a single sector. This concentration heightens risk and limits exposure to broader market opportunities.';
      }

      // Calculate the actual diversity score from portfolio metrics
      const diversityScore = Math.round(portfolio.metrics?.diversification?.score ?? 0);
      const diversityStatus = getScoreStatus(diversityScore);

      // Return content for Diversification Score
      return (
        <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 lg:p-5 h-full flex flex-col shadow-lg">
          {/* Metric Card - matching dashboard design */}
          <Card
            className="bg-slate-800/90 border-slate-700/40 hover:bg-slate-800/80 transition-all cursor-pointer
                     shadow-md shadow-slate-900/40 hover:shadow-lg hover:shadow-slate-900/50
                     transform hover:-translate-y-1 group mb-5"
            onClick={() => setShowMethodologyModal('Category Diversity')}
          >
            <CardContent className="p-5">
              <div className="flex items-center gap-2 mb-3">
                <div className={cn("p-1.5 rounded-md", diversityStatus.bgColor)}>
                  <BarChart2 className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-white">Category Diversity</h4>
                </div>
                <ChevronRight className="h-4 w-4 text-slate-400 transition-colors group-hover:text-slate-200" />
              </div>

              <div className="space-y-2">
                <div className="flex items-baseline justify-between">
                  <span className={cn("text-2xl font-bold", diversityStatus.color)}>
                    {diversityScore}
                  </span>
                  <span className={cn("text-xs", diversityStatus.color)}>
                    {diversityStatus.label}
                  </span>
                </div>

                <Progress
                  key={`diversity-${diversityScore}`}
                  value={diversityScore}
                  className="h-1.5 bg-slate-700/50"
                />
              </div>
            </CardContent>
          </Card>
          <div className="mb-5">
            <h4 className="text-xs font-medium text-slate-300 mb-2 flex items-center gap-2">
              <BarChart2 className="h-3.5 w-3.5 text-blue-400" aria-hidden="true" />
              <span>Summary</span>
            </h4>
            <div className="bg-slate-800/40 rounded-md p-3 border border-slate-700/30 shadow-md">
              <p className="text-xs text-slate-300 leading-relaxed">
                {description}
              </p>
            </div>
          </div>
          {/* Recommendations Section - Category Diversification Fallback */}
          <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 mb-5">
            <h4 className="text-sm font-medium text-white mb-3">Recommendations</h4>
            <div className="space-y-2 text-sm text-slate-300">
              <div className="flex items-start gap-2">
                <span className="text-xs">• {sectorCount < 3 ? 'Add 2-3 more sectors for improved diversification' : 'Consider adding exposure to 1-2 more categories'}</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">✓ Maintain balanced allocation across current sectors</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">ℹ Monitor sector performance quarterly for rebalancing opportunities</span>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (selectedMetric === 'investment-balance') {
      // Calculate portfolio weight score from market cap distribution
      const marketCapDist = (portfolio as any).market_cap_distribution;
      const portfolioWeightScore = marketCapDist
        ? Math.round(Object.values(marketCapDist).reduce((sum: number, val: any) => sum + (typeof val === 'number' ? val : 0), 0))
        : 0;
      const portfolioWeightStatus = getScoreStatus(portfolioWeightScore);

      // Return standardized content for Portfolio Weight - Matches Market Cap design
      return (
        <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 lg:p-5 h-full flex flex-col shadow-lg">
          {/* Portfolio Weight Score Card - matching dashboard design */}
          <Card
            className="bg-slate-800/90 border-slate-700/40 hover:bg-slate-800/80 transition-all cursor-pointer
                     shadow-md shadow-slate-900/40 hover:shadow-lg hover:shadow-slate-900/50
                     transform hover:-translate-y-1 group mb-5"
            onClick={() => setShowMethodologyModal('Portfolio Weight')}
          >
            <CardContent className="p-5">
              <div className="flex items-center gap-2 mb-3">
                <div className={cn("p-1.5 rounded-md", portfolioWeightStatus.bgColor)}>
                  <BarChart2 className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-white">Portfolio Weight</h4>
                </div>
                <ChevronRight className="h-4 w-4 text-slate-400 transition-colors group-hover:text-slate-200" />
              </div>

              <div className="space-y-2">
                <div className="flex items-baseline justify-between">
                  <span className={cn("text-2xl font-bold", portfolioWeightStatus.color)}>
                    {portfolioWeightScore}
                  </span>
                  <span className={cn("text-xs", portfolioWeightStatus.color)}>
                    {portfolioWeightStatus.label}
                  </span>
                </div>

                <Progress
                  key={`portfolio-weight-${portfolioWeightScore}-${portfolioWeightStatus.progressHex}`}
                  value={portfolioWeightScore}
                  className="h-1.5 bg-slate-700/50"
                />
              </div>
            </CardContent>
          </Card>
          {/* Summary Section - Matches MetricDescription and Recommendations structure */}
          <div className="mb-5">
            <h4 className="text-xs font-medium text-slate-300 mb-2 flex items-center gap-2">
              <BarChart2 className="h-3.5 w-3.5 text-blue-400" aria-hidden="true" />
              <span>Summary</span>
            </h4>
            <div className="bg-slate-800/40 rounded-md p-3 border border-slate-700/30 shadow-md">
              <p className="text-xs text-slate-300 leading-relaxed">
                Your top 5 assets represent 52.4% of your portfolio. This distribution provides good balance
                with moderate concentration risk. Optimal portfolios typically have less than 50% in their top
                5 assets.
              </p>
            </div>
          </div>
          {/* Recommendations Section */}
          {/* Recommendations Section - Asset Distribution Fallback */}
          <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 mb-5">
            <h4 className="text-sm font-medium text-white mb-3">Recommendations</h4>
            <div className="space-y-2 text-sm text-slate-300">
              <div className="flex items-start gap-2">
                <span className="text-xs">ℹ Reduce concentration in top 3 assets for better balance</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">• Distribute funds to smaller positions or new assets</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">ℹ Aim for less than 65% allocation in your top 5 assets</span>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (selectedMetric === 'stablecoin-ratio') {
      // Calculate stablecoin ratio score from portfolio metrics
      const stablecoinScore = Math.round(portfolio.metrics?.stablecoinRatio?.score ?? 0);
      const stablecoinStatus = getScoreStatus(stablecoinScore);

      // Return content for Stablecoin Ratio - Standardized to match Market Cap design
      return (
        <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 lg:p-5 h-full flex flex-col shadow-lg">
          {/* Stablecoin Ratio Score Card - matching dashboard design */}
          <Card
            className="bg-slate-800/90 border-slate-700/40 hover:bg-slate-800/80 transition-all cursor-pointer
                     shadow-md shadow-slate-900/40 hover:shadow-lg hover:shadow-slate-900/50
                     transform hover:-translate-y-1 group mb-5"
            onClick={() => setShowMethodologyModal('Stablecoin Ratio')}
          >
            <CardContent className="p-5">
              <div className="flex items-center gap-2 mb-3">
                <div className={cn("p-1.5 rounded-md", stablecoinStatus.bgColor)}>
                  <Shield className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-white">Stablecoin Ratio</h4>
                </div>
                <ChevronRight className="h-4 w-4 text-slate-400 transition-colors group-hover:text-slate-200" />
              </div>

              <div className="space-y-2">
                <div className="flex items-baseline justify-between">
                  <span className={cn("text-2xl font-bold", stablecoinStatus.color)}>
                    {stablecoinScore}
                  </span>
                  <span className={cn("text-xs", stablecoinStatus.color)}>
                    {stablecoinStatus.label}
                  </span>
                </div>

                <Progress
                  key={`stablecoin-${stablecoinScore}`}
                  value={stablecoinScore}
                  className="h-1.5 bg-slate-700/50"
                />
              </div>
            </CardContent>
          </Card>
          {/* Summary Section - Matches MetricDescription and Recommendations structure */}
          <div className="mb-5">
            <h4 className="text-xs font-medium text-slate-300 mb-2 flex items-center gap-2">
              <BarChart2 className="h-3.5 w-3.5 text-blue-400" aria-hidden="true" />
              <span>Summary</span>
            </h4>
            <div className="bg-slate-800/40 rounded-md p-3 border border-slate-700/30 shadow-md">
              <p className="text-xs text-slate-300 leading-relaxed">
                Your portfolio maintains a healthy 15% stablecoin allocation, providing both stability and
                purchasing power for market opportunities. This ratio balances risk management with growth potential.
              </p>
            </div>
          </div>
          {/* Recommendations Section */}
          {/* Recommendations Section - Stablecoin Ratio Fallback */}
          <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 mb-5">
            <h4 className="text-sm font-medium text-white mb-3">Recommendations</h4>
            <div className="space-y-2 text-sm text-slate-300">
              <div className="flex items-start gap-2">
                <span className="text-xs">✓ Current stablecoin ratio (15%) is optimal for your risk profile</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">ℹ Consider diversifying stablecoins across multiple protocols</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">ℹ Maintain this ratio to enable buying during market corrections</span>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (selectedMetric === 'asset') {
      // Calculate Portfolio Quality score from portfolio metrics
      const portfolioQualityScore = Math.round(portfolio.metrics?.portfolio_quality?.score ?? 0);
      const portfolioQualityStatus = getScoreStatus(portfolioQualityScore);

      // Return content for Portfolio Quality - Matches dashboard design
      return (
        <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 lg:p-5 h-full flex flex-col shadow-lg">
          {/* Portfolio Quality Score Card - matching dashboard design */}
          <Card
            className="bg-slate-800/90 border-slate-700/40 hover:bg-slate-800/80 transition-all cursor-pointer
                     shadow-md shadow-slate-900/40 hover:shadow-lg hover:shadow-slate-900/50
                     transform hover:-translate-y-1 group mb-5"
            onClick={() => setShowMethodologyModal('Portfolio Quality')}
          >
            <CardContent className="p-5">
              <div className="flex items-center gap-2 mb-3">
                <div className={cn("p-1.5 rounded-md", portfolioQualityStatus.bgColor)}>
                  <Diamond className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-white">Portfolio Quality</h4>
                </div>
                <ChevronRight className="h-4 w-4 text-slate-400 transition-colors group-hover:text-slate-200" />
              </div>

              <div className="space-y-2">
                <div className="flex items-baseline justify-between">
                  <span className={cn("text-2xl font-bold", portfolioQualityStatus.color)}>
                    {portfolioQualityScore}
                  </span>
                  <span className={cn("text-xs", portfolioQualityStatus.color)}>
                    {portfolioQualityStatus.label}
                  </span>
                </div>

                <Progress
                  key={`quality-${portfolioQualityScore}`}
                  value={portfolioQualityScore}
                  className="h-1.5 bg-slate-700/50"
                />
              </div>
            </CardContent>
          </Card>
          {/* Summary Section - Matches MetricDescription and Recommendations structure */}
          <div className="mb-5">
            <h4 className="text-xs font-medium text-slate-300 mb-2 flex items-center gap-2">
              <BarChart2 className="h-3.5 w-3.5 text-blue-400" aria-hidden="true" />
              <span>Summary</span>
            </h4>
            <div className="bg-slate-800/40 rounded-md p-3 border border-slate-700/30 shadow-md">
              <p className="text-xs text-slate-300 leading-relaxed">
                Your portfolio demonstrates strong quality with high-scoring projects. The weighted average
                score reflects the overall strength of your crypto investments based on CoinScout's comprehensive analysis.
              </p>
            </div>
          </div>
          {/* Recommendations Section - Fallback implementation */}
          <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 mb-5">
            <h4 className="text-sm font-medium text-white mb-3">Recommendations</h4>
            <div className="space-y-2 text-sm text-slate-300">
              <div className="flex items-start gap-2">
                <span className="text-xs">✓ Continue holding high-quality projects with strong fundamentals</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">ℹ Consider reallocating from lower-scoring assets to higher-quality alternatives</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">• Research emerging projects with high potential scores for diversification</span>
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      // Default or other metrics (asset, category) - Standardized to match Market Cap design
      return (
        <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 lg:p-5 h-full flex flex-col shadow-lg">
          {/* Score Card */}
          {/* Score Progress Card - Fallback implementation */}
          <Card
            className="bg-slate-800/90 border-slate-700/40 hover:bg-slate-800/80 transition-all cursor-pointer
                     shadow-md shadow-slate-900/40 hover:shadow-lg hover:shadow-slate-900/50
                     transform hover:-translate-y-1 group mb-5"
            onClick={() => {
              const currentMetric = metricTabs.find(tab => tab.id === selectedMetric)?.name;
              if (currentMetric) {
                setShowMethodologyModal(currentMetric);
              }
            }}
          >
            <CardContent className="p-5">
              <div className="flex items-center gap-2 mb-3">
                <div className={cn("p-1.5 rounded-md", getScoreStatus(score).bgColor)}>
                  <BarChart2 className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-white">{metricTabs.find(tab => tab.id === selectedMetric)?.name || "Score"}</h4>
                </div>
                <ChevronRight className="h-4 w-4 text-slate-400 transition-colors group-hover:text-slate-200" />
              </div>

              <div className="space-y-2">
                <div className="flex items-baseline justify-between">
                  <span className={cn("text-2xl font-bold", getScoreStatus(score).color)}>
                    {score}
                  </span>
                  <span className={cn("text-xs", getScoreStatus(score).color)}>
                    {getScoreStatus(score).label}
                  </span>
                </div>

                <Progress
                  value={score}
                  className="h-1.5 bg-slate-700/50"
                />
              </div>
            </CardContent>
          </Card>
          {/* Summary Section - Matches MetricDescription and Recommendations structure */}
          <div className="mb-5">
            <h4 className="text-xs font-medium text-slate-300 mb-2 flex items-center gap-2">
              <BarChart2 className="h-3.5 w-3.5 text-blue-400" aria-hidden="true" />
              <span>Summary</span>
            </h4>
            <div className="bg-slate-800/40 rounded-md p-3 border border-slate-700/30 shadow-md">
              <p className="text-xs text-slate-300 leading-relaxed">{summaryText}</p>
            </div>
          </div>
          {/* Recommendations Section - Fallback implementation */}
          <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 mb-5">
            <h4 className="text-sm font-medium text-white mb-3">Recommendations</h4>
            <div className="space-y-2 text-sm text-slate-300">
              <div className="flex items-start gap-2">
                <span className="text-xs">ℹ Review allocation regularly to maintain optimal distribution</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">• Consider diversifying into additional assets for better balance</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-xs">✓ Current portfolio structure shows good alignment with market</span>
              </div>
            </div>
          </div>
        </div>
      );
    }
  };
  return (
    <Card className={cn("bg-slate-800/60 border-slate-700/20 overflow-hidden hover:bg-slate-800/70 p-4 md:p-5", className)}>
      <div className="flex flex-col lg:flex-row w-full items-start gap-4 md:gap-5">
        {/* Left Sidebar - 20% width */}
        <div className="w-full lg:w-1/5 h-fit">
          <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 lg:p-5 shadow-lg h-auto">
            <div className="space-y-2">
              {metricTabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setSelectedMetric(tab.id as any)}
                  className={cn(
                    "w-full text-left px-3 py-2.5 rounded-md transition-all duration-200",
                    "flex items-center gap-3 group",
                    selectedMetric === tab.id
                      ? "bg-slate-700/50 text-white"
                      : "text-slate-400 hover:bg-slate-700/30 hover:text-slate-300"
                  )}
                >
                  {tab.icon}
                  <span className="text-sm font-medium">{tab.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
        {/* Center Content Area - Full width for coinscore, otherwise 70% */}
        <div id="metric-content-panel"
          className={`w-full ${selectedMetric === 'coinscore' ? 'lg:w-[80%]' : 'lg:w-[70%]'} h-auto self-start bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 lg:p-5 shadow-lg`}
          role="region"
          aria-label={`${metricTabs.find(tab => tab.id === selectedMetric)?.name} visualization`}>

          {/* Overall portfolio (coinscore) tab content */}
          {selectedMetric === 'coinscore' && (() => {
            // Overall portfolio tab with main score and 6 metric cards
            const mainScore = (portfolio.overallScore !== null && portfolio.overallScore !== undefined) ? Math.round(portfolio.overallScore * 10) / 10 : null;
            const mainStatus = mainScore ? (mainScore >= 75 ? 'Positive' : mainScore >= 65 ? 'Average' : 'Weak') : 'Loading...';
            const mainStatusColor = mainScore ? (mainScore >= 75 ? '#00E5E5' : mainScore >= 65 ? '#fbbf24' : '#ef4444') : '#64748b';

            const metrics = [
              {
                name: 'Portfolio Quality',
                score: Math.round((portfolio.metrics?.portfolio_quality?.score ?? 87) * 10) / 10,
                icon: ChartBar,
                description: 'We evaluate the fundamental quality and CoinScout scores of all projects in your portfolio. This includes team credentials, technology innovation, market adoption, and overall project health indicators.',
                importance: [
                  'Quality Projects: High quality projects with strong fundamentals tend to survive market downturns better.',
                  'Long-term Performance: Projects with solid teams and technology typically deliver better long-term returns.',
                  'Risk Mitigation: Avoiding low-quality projects reduces the risk of significant losses from project failures.'
                ],
                scoringLevels: [
                  { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
                  { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
                  { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
                  { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
                ]
              },
              {
                name: 'Market Cap',
                score: Math.round((portfolio.metrics?.marketCapDistribution?.score ?? 89) * 10) / 10,
                icon: Coins,
                description: 'Analysis of market capitalization distribution across your portfolio, balancing large-cap stability with small-cap growth potential.',
                importance: [
                  'Risk Management: Large-cap assets provide stability during market volatility.',
                  'Growth Potential: Small and mid-cap assets offer higher growth opportunities.',
                  'Balanced Exposure: Proper distribution across market caps optimizes risk-reward ratio.'
                ],
                scoringLevels: [
                  { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
                  { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
                  { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
                  { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
                ]
              },
              {
                name: 'Trend Category',
                score: (() => {
                  const trendingCategories = (portfolio as any).trending_categories;
                  if (trendingCategories && trendingCategories.length > 0) {
                    const totalAllocation = trendingCategories.reduce((sum: number, cat: any) => sum + cat.allocation, 0);
                    return Math.round(Math.min(totalAllocation * 2, 100) * 10) / 10;
                  }
                  return 92;
                })(),
                icon: TrendingUp,
                description: 'Tracking sectors which provides both growth potential from emerging technologies and stability from established assets. This balanced approach helps reduce overall market vulnerability.',
                importance: [
                  'Market Alpha: Trending sectors often outperform the broader market.',
                  'Innovation Exposure: Early positioning in emerging technologies can yield significant returns.',
                  'Momentum Trading: Trending categories attract more investment and attention.'
                ],
                scoringLevels: [
                  { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
                  { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
                  { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
                  { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
                ]
              },
              {
                name: 'Category Diversity',
                score: Math.round((portfolio.metrics?.category_diversification?.score ?? 85) * 10) / 10,
                icon: BarChart3,
                description: 'Measuring the breadth of sectors in your portfolio. Higher diversity reduces concentration risk and captures opportunities across the crypto ecosystem.',
                importance: [
                  'Risk Distribution: Diverse sectors protect against sector-specific downturns.',
                  'Opportunity Capture: Exposure to multiple sectors increases chances of catching breakout trends.',
                  'Portfolio Stability: Diversification reduces overall portfolio volatility.'
                ],
                scoringLevels: [
                  { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
                  { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
                  { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
                  { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
                ]
              },
              {
                name: 'Portfolio Weight',
                score: (() => {
                  const marketCapDist = (portfolio as any).market_cap_distribution;
                  return marketCapDist
                    ? Math.round(Object.values(marketCapDist).reduce((sum: number, val: any) => sum + (typeof val === 'number' ? val : 0), 0) * 10) / 10
                    : 90;
                })(),
                icon: Scale,
                description: 'Evaluating asset allocation balance to prevent overconcentration in any single asset, ensuring no position poses excessive risk.',
                importance: [
                  'Concentration Risk: Overweighted positions can lead to significant losses.',
                  'Rebalancing Efficiency: Proper weights allow for easier portfolio management.',
                  'Risk-Adjusted Returns: Optimal weighting maximizes returns for given risk level.'
                ],
                scoringLevels: [
                  { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
                  { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
                  { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
                  { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
                ]
              },
              {
                name: 'Stablecoin Ratio',
                score: Math.round((portfolio.metrics?.stablecoinRatio?.score ?? 92) * 10) / 10,
                icon: DollarSign,
                description: 'Assessing the percentage of stablecoins in your portfolio as a risk management tool and liquidity buffer.',
                importance: [
                  'Risk Buffer: Stablecoins provide safety during market downturns.',
                  'Buying Power: Maintains liquidity for opportunistic purchases.',
                  'Portfolio Stability: Reduces overall portfolio volatility.'
                ],
                scoringLevels: [
                  { range: '90% - 100%', status: 'Excellent', color: '#22c55e' },
                  { range: '75% - 89%', status: 'Positive', color: '#00E5E5' },
                  { range: '65% - 74%', status: 'Average', color: '#fbbf24' },
                  { range: '0% - 64%', status: 'Weak', color: '#ef4444' }
                ]
              }
            ];

            return (
              <div className="h-full flex flex-col">
                {/* Main Score Display - Clickable with consistent styling */}
                <Card
                  className="bg-slate-800/90 border-slate-700/40 hover:bg-slate-800/80 transition-all cursor-pointer
                           shadow-md shadow-slate-900/40 hover:shadow-lg hover:shadow-slate-900/50
                           transform hover:-translate-y-1 group mb-6"
                  onClick={() => setShowMethodologyModal('Overall Portfolio')}
                >
                  <CardContent className="p-5">
                    <div className="text-center">
                      {mainScore !== null ? (
                        <>
                          <div className="flex items-baseline justify-center gap-2 mb-2">
                            <span className="text-7xl font-bold" style={{ color: mainStatusColor }}>
                              {mainScore}
                            </span>
                            <span className="text-xl text-slate-400">out of 100</span>
                            <MousePointerClick className="h-6 w-6 text-slate-400 ml-2 transition-colors group-hover:text-slate-200" />
                          </div>
                          <div className="text-lg font-medium" style={{ color: mainStatusColor }}>
                            {mainStatus}
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="flex items-center justify-center gap-3 mb-4">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
                            <span className="text-xl text-slate-400">Calculating score...</span>
                          </div>
                          <div className="text-lg font-medium text-slate-400">
                            Loading...
                          </div>
                        </>
                      )}

                      {/* Progress Bar */}
                      <div className="w-full mt-4 mb-4">
                        <div className="h-2 bg-slate-700 rounded-full overflow-hidden">
                          {mainScore !== null ? (
                            <div
                              className="h-full rounded-full animate-grow-width"
                              style={{
                                backgroundColor: mainStatusColor,
                                '--target-width': `${mainScore}%`,
                                animationDelay: '0.2s'
                              } as React.CSSProperties}
                            />
                          ) : (
                            <div className="h-full bg-slate-600 rounded-full animate-pulse" />
                          )}
                        </div>
                      </div>

                      {/* Info Text */}
                      <div className="flex items-center justify-center gap-2 text-sm text-slate-300">
                        <Info className="h-4 w-4" />
                        <span>Review metrics below 75% for improvement opportunities.</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Metric Cards Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 flex-1">
                  {metrics.map((metric, index) => {
                    const Icon = metric.icon;
                    const actualScore = metric.name === 'Portfolio Quality' ? portfolio.metrics?.portfolio_quality?.score :
                                       metric.name === 'Market Cap' ? portfolio.metrics?.marketCapDistribution?.score :
                                       metric.name === 'Trend Category' ? (() => {
                                         const trendingCategories = (portfolio as any).trending_categories;
                                         if (trendingCategories && trendingCategories.length > 0) {
                                           const totalAllocation = trendingCategories.reduce((sum: number, cat: any) => sum + cat.allocation, 0);
                                           return Math.min(totalAllocation * 2, 100);
                                         }
                                         return null;
                                       })() :
                                       metric.name === 'Category Diversity' ? portfolio.metrics?.category_diversification?.score :
                                       metric.name === 'Portfolio Weight' ? (() => {
                                         const marketCapDist = (portfolio as any).market_cap_distribution;
                                         return marketCapDist
                                           ? Object.values(marketCapDist).reduce((sum: number, val: any) => sum + (typeof val === 'number' ? val : 0), 0)
                                           : null;
                                       })() :
                                       metric.name === 'Stablecoin Ratio' ? portfolio.metrics?.stablecoinRatio?.score :
                                       null;

                    const displayScore = (actualScore !== null && actualScore !== undefined) ? Math.round(actualScore * 10) / 10 : null;
                    const status = displayScore ? (displayScore >= 90 ? 'Excellent' :
                                 displayScore >= 75 ? 'Positive' :
                                 displayScore >= 65 ? 'Average' : 'Weak') : 'Loading...';
                    const statusColor = displayScore ? (displayScore >= 90 ? '#22c55e' :
                                      displayScore >= 75 ? '#00E5E5' :
                                      displayScore >= 65 ? '#fbbf24' : '#ef4444') : '#64748b';

                    return (
                      <Card
                        key={index}
                        className="bg-slate-800/90 border-slate-700/40 hover:bg-slate-800/80 transition-all cursor-pointer
                                 shadow-md shadow-slate-900/40 hover:shadow-lg hover:shadow-slate-900/50
                                 transform hover:-translate-y-1 group"
                        onClick={() => setShowMethodologyModal(metric.name)}
                      >
                        <CardContent className="p-5 h-full flex flex-col">
                          <div className="flex items-center gap-2 mb-3">
                            <div className="p-1.5 rounded-md bg-slate-700/50">
                              <Icon className="h-4 w-4 text-slate-300" />
                            </div>
                            <h4 className="text-sm font-medium text-white flex-1">{metric.name}</h4>
                            <ChevronRight className="h-4 w-4 text-slate-400 transition-colors group-hover:text-slate-200" />
                          </div>

                          <div className="mt-auto">
                            {displayScore !== null ? (
                              <>
                                <div className="flex items-baseline justify-between mb-2">
                                  <span className="text-3xl font-bold" style={{ color: statusColor }}>
                                    {displayScore}
                                  </span>
                                  <span className="text-sm" style={{ color: statusColor }}>
                                    {status}
                                  </span>
                                </div>
                                <Progress
                                  value={displayScore}
                                  className="h-1.5 bg-slate-700/50"
                                />
                              </>
                            ) : (
                              <>
                                <div className="flex items-center justify-center mb-2">
                                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
                                </div>
                                <div className="h-1.5 bg-slate-700/50 rounded-full animate-pulse" />
                              </>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>

                {/* Methodology Modal */}
                {showMethodologyModal && (
                  <Dialog open={!!showMethodologyModal} onOpenChange={() => setShowMethodologyModal(null)}>
                    <DialogContent className="bg-slate-900/95 border-slate-700/40 text-white max-w-2xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle className="text-xl font-semibold flex items-center gap-3">
                          {(() => {
                            const metric = metrics.find(m => m.name === showMethodologyModal);
                            if (!metric) return null;
                            const Icon = metric.icon;
                            const metricScore = metric.score ?? 0;
                            const status = metricScore >= 90 ? 'Excellent' :
                                         metricScore >= 75 ? 'Positive' :
                                         metricScore >= 65 ? 'Average' : 'Weak';
                            const statusColor = metricScore >= 90 ? '#22c55e' :
                                              metricScore >= 75 ? '#00E5E5' :
                                              metricScore >= 65 ? '#fbbf24' : '#ef4444';

                            return (
                              <>
                                <Icon className="h-5 w-5" />
                                <span>{metric.name}</span>
                                <Badge
                                  className="ml-auto"
                                  style={{
                                    backgroundColor: `${statusColor}20`,
                                    color: statusColor,
                                    borderColor: statusColor
                                  }}
                                >
                                  {status}
                                </Badge>
                              </>
                            );
                          })()}
                        </DialogTitle>
                      </DialogHeader>

                      {(() => {
                        const metric = metrics.find(m => m.name === showMethodologyModal);
                        if (!metric) return null;
                        const metricScore = metric.score ?? 0;

                        return (
                          <div className="space-y-6 mt-6">
                            {/* Score Display */}
                            <div className="text-center py-4 bg-slate-800/50 rounded-lg">
                              <div className="text-3xl font-bold mb-1" style={{
                                color: metricScore >= 90 ? '#22c55e' :
                                      metricScore >= 75 ? '#00E5E5' :
                                      metricScore >= 65 ? '#fbbf24' : '#ef4444'
                              }}>
                                {metricScore} / 100
                              </div>
                              <div className="text-sm text-slate-400">{metric.name} SCORE</div>
                            </div>

                            {/* What Are We Scoring */}
                            <div>
                              <h3 className="text-base font-semibold mb-3 flex items-center gap-2">
                                <Target className="h-4 w-4 text-blue-400" />
                                What Are We Scoring?
                              </h3>
                              <p className="text-sm text-slate-300 leading-relaxed">
                                {metric.description}
                              </p>
                            </div>

                            {/* Why Is This Important */}
                            <div>
                              <h3 className="text-base font-semibold mb-3 flex items-center gap-2">
                                <Info className="h-4 w-4 text-yellow-400" />
                                Why Is This Important?
                              </h3>
                              <ul className="space-y-2">
                                {metric.importance.map((item, idx) => (
                                  <li key={idx} className="flex items-start gap-2 text-sm text-slate-300">
                                    <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                                    <span>{item}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>

                            {/* Scoring Levels */}
                            <div>
                              <h3 className="text-base font-semibold mb-3 flex items-center gap-2">
                                <BarChart3 className="h-4 w-4 text-purple-400" />
                                Scoring Levels
                              </h3>
                              <div className="space-y-2">
                                {metric.scoringLevels.map((level, idx) => (
                                  <div key={idx} className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
                                    <div className="flex items-center gap-3">
                                      <div
                                        className="w-3 h-3 rounded-full"
                                        style={{ backgroundColor: level.color }}
                                      />
                                      <span className="text-sm font-medium" style={{ color: level.color }}>
                                        {level.status}
                                      </span>
                                    </div>
                                    <span className="text-sm text-slate-400">{level.range}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        );
                      })()}
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            );
          })()}

          {chartData.length > 0 && chartData[0]?.displayAsBarChart && (
            <div className="flex flex-col gap-6 p-4">
              {/* Market Cap Distribution Section */}
              <div className="h-full">
                <div className="flex items-center gap-1.5 mb-4 lg:mb-6">
                  <div className="bg-blue-500/10 p-1.5 rounded-md">
                    <BarChart2 className="h-4 w-4 text-blue-400" />
                  </div>
                  <h2 className="text-white text-lg font-medium">Market Cap Distribution</h2>
                </div>
                {/* Embed Chart Inside Styled Container - matched width with Market Cap Allocation container */}
                <div className="p-4 pt-6 bg-slate-800/30 rounded-md border border-slate-700/20 shadow-md">
                  {/* Blue info card removed - chart area now has more space and cleaner appearance */}
                  <div className="h-[250px] sm:h-[300px] w-full">
                    <div
                      className="flex h-full gap-3 sm:gap-5 md:gap-7 lg:gap-9 justify-center"
                      role="group"
                      aria-label="Market cap distribution chart comparing portfolio allocation to recommended values"
                    >
                      {chartData.map((item, index) => (
                        <div key={index} className="flex flex-col items-center gap-1 sm:gap-2 w-1/3">
                          <div className="h-[200px] sm:h-[250px] w-[60px] sm:w-[80px] md:w-[88px] flex flex-col-reverse relative">
                            {/* Recommended bar (background) */}
                            <div className="absolute inset-0 flex flex-col-reverse">
                              <div
                                className="w-full animate-grow-height"
                                style={{
                                  height: `${item.recommended || 0}%`,
                                  backgroundColor: PORTFOLIO_COLORS.chart.benchmark,
                                  maxHeight: '250px',
                                  opacity: 0.8,
                                  animationDelay: `${0.2 + (index * 0.15)}s`
                                } as React.CSSProperties}
                                role="graphics-symbol"
                                aria-label={`${item.name} recommended allocation: ${item.recommended || 0}%`}
                              />
                            </div>
                            {/* Current allocation bar (foreground) */}
                            <div className="absolute inset-0 flex flex-col-reverse">
                              <div
                                className="w-full animate-grow-height"
                                style={{
                                  height: `${item.value}%`,
                                  backgroundColor: PORTFOLIO_COLORS.chart.portfolio,
                                  maxHeight: '250px',
                                  animationDelay: `${0.4 + (index * 0.15)}s`
                                } as React.CSSProperties}
                                role="graphics-symbol"
                                aria-label={`${item.name} current allocation: ${item.value}%`}
                              />
                            </div>
                            {/* Percentage value labels */}
                            <div className="absolute top-2 left-0 right-0 flex justify-center">
                              <div className="bg-slate-800/95 border border-slate-700/50 rounded-md px-2 py-1.5 shadow-lg">
                                <div className="flex flex-col gap-1.5">
                                  {/* Current allocation label */}
                                  <div className="flex items-center gap-1.5">
                                    <div className="w-2 h-2 rounded-full" style={{ backgroundColor: PORTFOLIO_COLORS.chart.portfolio }}></div>
                                    <span className="text-[10px] text-slate-300">Current:</span>
                                    <span className="text-[10px] sm:text-xs font-semibold text-white">
                                      {Math.round(item.value * 10) / 10}%
                                    </span>
                                  </div>
                                  {/* Recommended allocation label */}
                                  <div className="flex items-center gap-1.5">
                                    <div className="w-2 h-2 rounded-full" style={{ backgroundColor: PORTFOLIO_COLORS.chart.benchmark }}></div>
                                    <span className="text-[10px] text-slate-300">Recommended:</span>
                                    <span className="text-[10px] sm:text-xs font-semibold text-white">
                                      {Math.round((item.recommended || 0) * 10) / 10}%
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="text-xs sm:text-sm font-medium text-slate-200 mt-1 sm:mt-2">{item.name}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                  {/* Legend moved inside the container - at the bottom */}
                  <div className="flex flex-wrap items-center gap-3 md:gap-4 mt-4 justify-center">
                    <div className="flex items-center">
                      <div className="w-3 h-3 md:w-3 md:h-3 mr-2" style={{ backgroundColor: PORTFOLIO_COLORS.chart.portfolio }} aria-hidden="true" />
                      <span className="text-xs font-medium text-slate-300">Current Allocation</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 md:w-3 md:h-3 mr-2" style={{ backgroundColor: PORTFOLIO_COLORS.chart.benchmark }} aria-hidden="true" />
                      <span className="text-xs font-medium text-slate-300">Recommended</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          {/* Trend Exposure Bar Chart */}
          {chartData.length > 0 && chartData[0]?.displayAsHorizontalBarChart && (
            <div className="h-full p-4">
              {/* Real API trending_categories data horizontal bar chart */}
              <div className="w-full">
                <div className="grid grid-cols-1 gap-3">
                  {chartData.map((entry, index) => {
                    const percent = entry.value;
                    const barWidth = `${Math.min(percent, 100)}%`;

                    return (
                      <div
                        key={`bar-${index}`}
                        className="bg-slate-800/60 hover:bg-slate-700/50 rounded-md p-3 border border-slate-700/30 transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-slate-900/40 relative group"
                      >
                        {/* Category title and percentage */}
                        <div className="flex items-center mb-2">
                          <div className="flex items-center space-x-1.5">
                            <span className="text-xs font-medium text-slate-200 truncate">
                              {entry.name}
                            </span>
                          </div>
                          <span className="ml-auto text-xs text-slate-300 font-semibold">
                            {entry.value.toFixed(1)}%
                          </span>
                        </div>

                        {/* Progress bar */}
                        <div className="h-2 w-full bg-slate-700/30 rounded-full mt-1 mb-2 overflow-hidden">
                          <div
                            className="h-full rounded-full transition-all duration-1000 ease-out"
                            style={{
                              backgroundColor: entry.color,
                              width: barWidth
                            }}
                          />
                        </div>

                        {/* Coin details */}
                        {entry.coins && entry.coins.length > 0 && (
                          <div className="flex items-center gap-2 mt-2">
                            <span className="text-xs text-slate-400">Coins:</span>
                            <div className="flex items-center gap-1">
                              {entry.coins.slice(0, 3).map((coin: any, coinIndex: number) => (
                                <span key={coinIndex} className="text-xs text-slate-300">
                                  {coin.symbol}
                                  {coinIndex < Math.min(entry.coins.length, 3) - 1 ? ',' : ''}
                                </span>
                              ))}
                              {entry.coins.length > 3 && (
                                <span className="text-xs text-slate-400">
                                  +{entry.coins.length - 3} more
                                </span>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
          {/* Trending Sectors Card Display */}
          {chartData.length > 0 && chartData[0]?.displayAsTrendingCard && (
            <div className="h-full p-4">
              <div className="w-full flex flex-col">
                <div className="flex items-center mb-4">
                  <div className="flex items-center gap-1.5">
                    <div className="bg-blue-500/10 p-1.5 rounded-md">
                      <TrendingUp className="h-4 w-4 text-blue-400" />
                    </div>
                    <h2 className="text-white text-lg font-medium">Top 10 Trending Categories</h2>
                  </div>
                  <TooltipProvider>
                    <UITooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" className="h-6 w-6 p-0 ml-1">
                          <HelpCircle className="h-3.5 w-3.5 text-slate-400" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p className="text-xs">Shows your portfolio's exposure to current high-growth sectors compared to benchmark allocations.</p>
                      </TooltipContent>
                    </UITooltip>
                  </TooltipProvider>
                </div>
                <div className="space-y-6 mt-2">
                  {chartData.map((item, index) => (
                    <div key={index}>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-white font-medium text-sm">{item.name}</span>
                        <div className="flex items-center">
                          <span className="text-blue-400 mr-1 text-sm">{Math.round(item.value * 10) / 10}%</span>
                          <span className="text-slate-400 text-sm">/ {Math.round(item.benchmarkValue! * 10) / 10}%</span>
                        </div>
                      </div>
                      <div className="relative w-full h-1 bg-slate-800 mb-2">
                        <div
                          className="absolute top-0 left-0 h-full"
                          style={{
                            width: `${Math.min((item.value / item.benchmarkValue!) * 100, 100)}%`,
                            backgroundColor: '#64748b' // Neutral slate-500 for percentage display
                          }}
                        />
                      </div>
                      <div className="flex justify-between">
                        <Badge variant="outline" className="whitespace-nowrap text-xs"
                          style={{
                            backgroundColor: item.performanceScore! >= 80 ? 'rgba(44, 224, 172, 0.15)' :
                                             item.performanceScore! >= 70 ? 'rgba(61, 149, 231, 0.15)' :
                                             'rgba(231, 168, 61, 0.15)',
                            borderColor: item.performanceScore! >= 80 ? 'rgba(44, 224, 172, 0.3)' :
                                        item.performanceScore! >= 70 ? 'rgba(61, 149, 231, 0.3)' :
                                        'rgba(231, 168, 61, 0.3)',
                            color: item.performanceScore! >= 80 ? PORTFOLIO_COLORS.chart.positive :
                                   item.performanceScore! >= 70 ? PORTFOLIO_COLORS.status.info :
                                   PORTFOLIO_COLORS.risk.moderate
                          }}>
                          {item.performanceScore! >= 80 ? 'High Performance' :
                           item.performanceScore! >= 70 ? 'Above Average' :
                           'Average Growth'}
                        </Badge>
                        <span className="text-sm font-medium"
                              style={{ color: item.performanceScore! >= 80 ? PORTFOLIO_COLORS.chart.positive :
                                              item.performanceScore! >= 70 ? PORTFOLIO_COLORS.status.info :
                                              PORTFOLIO_COLORS.risk.moderate }}>
                          {Math.round(item.performanceScore! * 10) / 10}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
                {/* AI Recommendations */}
                <div className="mt-6 bg-slate-700/20 rounded-lg border border-slate-700/30 p-4">
                  <div className="flex justify-between items-center mb-3">
                    <h3 className="text-sm font-medium text-white flex items-center">
                      <Info className="h-4 w-4 text-blue-400 mr-2" />
                      Sector Insights
                    </h3>
                    <Badge variant="outline" className="bg-blue-900/20 border-blue-800/40 text-blue-300">
                      <TrendingUp className="mr-1 h-3 w-3" />
                      Performance focused
                    </Badge>
                  </div>
                  <ul className="space-y-2">
                    <li className="text-xs text-slate-300 flex items-start gap-2">
                      <TrendingUp className="h-3.5 w-3.5 text-blue-400 mt-0.5" />
                      <span>AI &amp; Machine Learning allocation is below market benchmark, but performance is strong (85%).</span>
                    </li>
                    <li className="text-xs text-slate-300 flex items-start gap-2">
                      <TrendingUp className="h-3.5 w-3.5 text-blue-400 mt-0.5" />
                      <span>DeFi Protocols exposure is higher than benchmark, consider rebalancing for optimal allocation.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )}
          {chartData.length > 0 && chartData[0]?.displayAsSpecialRatio && (
            <div className="h-full p-4">
              <div className="w-full flex flex-col">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-1.5">
                    <div className="bg-blue-500/10 p-1.5 rounded-md">
                      <DollarSign className="h-4 w-4 text-blue-400" />
                    </div>
                    <h2 className="text-white text-lg font-medium">Stablecoin Ratio</h2>
                  </div>
                  <div className="text-2xl font-bold flex items-center gap-1" style={{ color: PORTFOLIO_COLORS.chart.positive }}>
                    <DollarSign className="h-4 w-4" />
                    <span>{Math.round((chartData[0].currentRatio ?? 0) * 10) / 10}%</span>
                  </div>
                </div>
                <div className="mb-8">
                  <div className="flex justify-between text-xs text-slate-300 mb-1">
                    <span>Current Ratio</span>
                    <span>{chartData[0].currentRatio ?? 0}% / {chartData[0].targetRatio ?? 20}%</span>
                  </div>
                  <div className="w-full h-1.5 bg-slate-700 rounded-full overflow-hidden">
                    <div
                      className="h-full rounded-full animate-grow-width"
                      style={{
                        backgroundColor: PORTFOLIO_COLORS.chart.portfolio,
                        '--target-width': `${((chartData[0].currentRatio ?? 0) / (chartData[0].targetRatio ?? 20)) * 100}%`,
                        animationDelay: '0.2s'
                      } as React.CSSProperties}
                    />
                  </div>
                </div>
                <div className="space-y-3 bg-slate-800/50 border border-slate-700/50 rounded-lg p-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-300">Recommended Range</span>
                    <span className="text-sm" style={{ color: PORTFOLIO_COLORS.chart.portfolio }}>{chartData[0].recommendedRange || '15-25%'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-300">Current Value</span>
                    <span className="text-sm text-white">${(chartData[0].currentValue ?? 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-300">Target Value</span>
                    <span className="text-sm text-white">${(chartData[0].targetValue || 31873.36).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center pt-2 border-t border-slate-700/30">
                    <span className="text-sm text-slate-300">Status</span>
                    <Badge variant="outline" className="bg-blue-900/20 border-blue-800/40 text-blue-300">
                      <Shield className="mr-1 h-3 w-3" />
                      {chartData[0].status === 'needs_more_stability' ? 'Needs more stability' :
                       chartData[0].status === 'overexposed' ? 'Overexposed to stablecoins' :
                       chartData[0].status === 'well_balanced' ? 'Well balanced' :
                       'Unknown status'}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          )}
          {!selectedMetric.includes('coinscore') &&
           selectedMetric !== 'trending-bar-chart' && // Exclude trending-bar-chart tab
           selectedMetric !== 'investment-balance' && // Exclude investment-balance tab to prevent redundant card
           selectedMetric !== 'asset' && // Exclude Portfolio Quality tab to prevent redundant card
           chartData.length > 0 &&
           !chartData[0]?.displayAsBarChart &&
           !chartData[0]?.displayAsSpecialRatio && (
            <div className="p-4">
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    innerRadius="45%"
                    outerRadius="75%"
                    paddingAngle={2}
                    dataKey="value"
                    animationBegin={0}
                    animationDuration={1000}
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        const data = payload[0].payload;
                        return (
                          <div className="bg-slate-800 p-3 rounded-lg border border-slate-700">
                            <p className="text-sm font-medium text-white">{data.name}</p>
                            <p className="text-sm text-slate-300">{data.value.toFixed(1)}%</p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          )}
          {/* Portfolio Quality section */}
          {(selectedMetric === 'asset') && (
            <div className="h-full p-4">
              <div className="flex flex-col">
                {/* High quality projects summary bar */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="text-sm font-medium text-slate-100">High-Quality Projects</h3>
                    <span className="text-sm font-semibold text-slate-100">
                      {(() => {
                        const highQualityAssets = portfolio.assets.filter(asset => asset.aiScore >= 80);
                        return highQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0).toFixed(1);
                      })()}%
                    </span>
                  </div>
                  <div className="h-2 w-full bg-slate-800/60 rounded-full mb-1 overflow-hidden">
                    <div
                      className="h-full rounded-full animate-grow-width"
                      style={{
                        backgroundColor: '#2ecc71',
                        '--target-width': `${(() => {
                          const highQualityAssets = portfolio.assets.filter(asset => asset.aiScore >= 80);
                          return highQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0);
                        })()}%`,
                        animationDelay: '0.2s'
                      } as React.CSSProperties}
                    />
                  </div>
                  <p className="text-xs text-slate-400">Recommended: 60%+ High-Quality Projects</p>
                </div>

                {/* Warning message when high-quality allocation is low */}
                {(() => {
                  const highQualityPercentage = portfolio.assets
                    .filter(asset => asset.aiScore >= 80)
                    .reduce((sum, asset) => sum + asset.allocation, 0);

                  if (highQualityPercentage < 40) {
                    return (
                      <div className="mb-4 p-3 bg-amber-900/20 border border-amber-700/30 rounded-md flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
                        <p className="text-xs text-amber-100/90">
                          Your portfolio contains a low percentage of high-quality projects. Consider reallocating toward more reliable assets to improve your overall risk profile.
                        </p>
                      </div>
                    );
                  }
                  return null;
                })()}

                {/* Portfolio Quality Distribution section */}
                <div className="w-full mb-6 bg-gradient-to-b from-slate-800/20 to-slate-900/10 rounded-lg p-4 border border-slate-700/20 backdrop-blur-sm shadow-md">
                  <h3 className="text-md font-semibold text-white mb-4">CoinScout Project Quality Distribution</h3>

                  <div className="h-[300px] w-full">
                    {chartData.length === 0 ? (
                      <div className="h-full flex flex-col items-center justify-center text-slate-400">
                        <PieChartIcon className="h-12 w-12 mb-2 opacity-50" />
                        <p>No allocation data available</p>
                      </div>
                    ) : (
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={chartData}
                            cx="50%"
                            cy="50%"
                            labelLine={true}
                            outerRadius={110}
                            innerRadius={70}
                            dataKey="value"
                            paddingAngle={2}
                            cornerRadius={0}
                            label={({ name, value, cx, cy, midAngle, innerRadius, outerRadius, percent, index }) => {
                              // Calculate position for external labels
                              const RADIAN = Math.PI / 180;
                              const radius = outerRadius * 1.2;
                              const x = cx + radius * Math.cos(-midAngle * RADIAN);
                              const y = cy + radius * Math.sin(-midAngle * RADIAN);

                              // Determine text color based on entry name to match segment colors
                              let textColor = "#fff";
                              if (name === 'High Quality') {
                                textColor = "#2ecc71"; // Green for High Quality (90-100%)
                              } else if (name === 'Good') {
                                textColor = "#3498db"; // Blue for Good (80-89%)
                              } else if (name === 'Medium') {
                                textColor = "#f1c40f"; // Yellow for Medium (70-79%)
                              } else if (name === 'Lower') {
                                textColor = "#e74c3c"; // Red for Lower (0-69%)
                              }

                              return (
                                <text
                                  x={x}
                                  y={y}
                                  fill={textColor}
                                  textAnchor={x > cx ? 'start' : 'end'}
                                  dominantBaseline="central"
                                  className="text-xs font-medium"
                                  style={{ textShadow: '0 1px 3px rgba(0,0,0,0.8)' }}
                                >
                                  {`${name} (${(percent * 100).toFixed(0)}%)`}
                                </text>
                              );
                            }}
                            animationBegin={200}
                            animationDuration={1200}
                            animationEasing="ease-out"
                          >
                            {chartData.map((entry, index) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={entry.color}
                                stroke="#0f172a"
                                strokeWidth={1}
                              />
                            ))}
                          </Pie>
                        </PieChart>
                      </ResponsiveContainer>
                    )}
                  </div>
                </div>
                {/* CoinScout Project Score Breakdown - Assets grouped by quality rating */}
                <div className="p-4 bg-slate-800/30 rounded-md border border-slate-700/20 shadow-md">
                  <div className="flex items-center mb-3">
                    <div className="flex items-center gap-1.5">
                      <div className="bg-blue-500/10 p-1.5 rounded-md">
                        <Award className="h-4 w-4 text-blue-400" />
                      </div>
                      <h3 className="text-sm font-medium text-white">CoinScout Project Score Breakdown</h3>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 gap-3">
                    {/* High Quality Section (90-100) */}
                    <div className="bg-slate-800/60 hover:bg-slate-700/50 rounded-md p-3 border border-slate-700/30 transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-slate-900/40">
                      <div className="flex items-center mb-2">
                        <div className="flex items-center space-x-1.5">
                          <div className="h-2.5 w-2.5 rounded-full" style={{ backgroundColor: '#2ecc71' }}></div>
                          <span className="text-xs font-medium text-slate-300">High Quality (90–100)</span>
                        </div>
                        <span className="ml-auto text-xs text-slate-300 font-semibold">
                          {(() => {
                            const highQualityAssets = portfolio.assets.filter(asset => asset.aiScore >= 90);
                            return highQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0).toFixed(2);
                          })()}%
                        </span>
                      </div>
                      {/* Progress bar for high quality category */}
                      <div className="h-2 w-full bg-slate-700/30 rounded-full mt-1 mb-2 overflow-hidden">
                        <div
                          className="h-full rounded-full animate-grow-width"
                          style={{
                            backgroundColor: '#64748b', // Neutral slate-500 for percentage display
                            '--target-width': `${(() => {
                              const highQualityAssets = portfolio.assets.filter(asset => asset.aiScore >= 90);
                              return highQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0);
                            })()}%`,
                            animationDelay: '0.2s'
                          } as React.CSSProperties}
                        />
                      </div>
                      <div className="space-y-1.5 mt-2">
                        {portfolio.assets.filter(asset => asset.aiScore >= 90).map((asset, index) => (
                          <div
                            key={`high-quality-${index}`}
                            className="flex items-center justify-between py-1.5 border-b border-slate-700/20 last:border-0 hover:bg-slate-800/40 transition-colors duration-300 rounded-sm"
                          >
                            <div className="flex items-center space-x-2">
                              <CoinLogo symbol={asset.symbol} size="sm" className="w-5 h-5" imageUrl={asset.logoUrl} />
                              <div className="flex flex-col">
                                <span className="text-xs font-medium text-white">{asset.symbol}</span>
                                <span className="text-[10px] text-slate-400 leading-tight">{asset.name}</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-xs font-medium text-slate-300">{asset.allocation}%</span>
                              {/* ScoreGauge - High Quality Fallback */}
                              <div className="flex items-center justify-center">
                                <div className={cn(
                                  "w-6 h-6 rounded-full border-2 flex items-center justify-center",
                                  asset.aiScore >= 90 ? "border-emerald-500 bg-emerald-500/20" :
                                  asset.aiScore >= 75 ? "border-blue-500 bg-blue-500/20" :
                                  asset.aiScore >= 65 ? "border-yellow-500 bg-yellow-500/20" :
                                  asset.aiScore >= 50 ? "border-orange-500 bg-orange-500/20" :
                                  "border-red-500 bg-red-500/20"
                                )}>
                                  <span className="text-xs font-bold text-white">{asset.aiScore}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                        {portfolio.assets.filter(asset => asset.aiScore >= 90).length === 0 && (
                          <div className="text-xs text-slate-500 italic text-center py-2">No high quality projects in portfolio</div>
                        )}
                      </div>
                    </div>

                    {/* Good Quality Section (80-89) */}
                    <div className="bg-slate-800/60 hover:bg-slate-700/50 rounded-md p-3 border border-slate-700/30 transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-slate-900/40">
                      <div className="flex items-center mb-2">
                        <div className="flex items-center space-x-1.5">
                          <div className="h-2.5 w-2.5 rounded-full" style={{ backgroundColor: '#3498db' }}></div>
                          <span className="text-xs font-medium text-slate-300">Good (80–89)</span>
                        </div>
                        <span className="ml-auto text-xs text-slate-300 font-semibold">
                          {(() => {
                            const goodQualityAssets = portfolio.assets.filter(asset => asset.aiScore >= 80 && asset.aiScore < 90);
                            return goodQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0).toFixed(2);
                          })()}%
                        </span>
                      </div>
                      {/* Progress bar for good quality category */}
                      <div className="h-2 w-full bg-slate-700/30 rounded-full mt-1 mb-2 overflow-hidden">
                        <div
                          className="h-full rounded-full animate-grow-width"
                          style={{
                            backgroundColor: '#64748b', // Neutral slate-500 for percentage display
                            '--target-width': `${(() => {
                              const goodQualityAssets = portfolio.assets.filter(asset => asset.aiScore >= 80 && asset.aiScore < 90);
                              return goodQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0);
                            })()}%`,
                            animationDelay: '0.25s'
                          } as React.CSSProperties}
                        />
                      </div>
                      <div className="space-y-1.5 mt-2">
                        {portfolio.assets.filter(asset => asset.aiScore >= 80 && asset.aiScore < 90).map((asset, index) => (
                          <div
                            key={`good-quality-${index}`}
                            className="flex items-center justify-between py-1.5 border-b border-slate-700/20 last:border-0 hover:bg-slate-800/40 transition-colors duration-300 rounded-sm"
                          >
                            <div className="flex items-center space-x-2">
                              <CoinLogo symbol={asset.symbol} size="sm" className="w-5 h-5" imageUrl={asset.logoUrl} />
                              <div className="flex flex-col">
                                <span className="text-xs font-medium text-white">{asset.symbol}</span>
                                <span className="text-[10px] text-slate-400 leading-tight">{asset.name}</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-xs font-medium text-slate-300">{asset.allocation}%</span>
                              {/* ScoreGauge - Good Quality Fallback */}
                              <div className="flex items-center justify-center">
                                <div className={cn(
                                  "w-6 h-6 rounded-full border-2 flex items-center justify-center",
                                  asset.aiScore >= 90 ? "border-emerald-500 bg-emerald-500/20" :
                                  asset.aiScore >= 75 ? "border-blue-500 bg-blue-500/20" :
                                  asset.aiScore >= 65 ? "border-yellow-500 bg-yellow-500/20" :
                                  asset.aiScore >= 50 ? "border-orange-500 bg-orange-500/20" :
                                  "border-red-500 bg-red-500/20"
                                )}>
                                  <span className="text-xs font-bold text-white">{asset.aiScore}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                        {portfolio.assets.filter(asset => asset.aiScore >= 80 && asset.aiScore < 90).length === 0 && (
                          <div className="text-xs text-slate-500 italic text-center py-2">No good quality projects in portfolio</div>
                        )}
                      </div>
                    </div>

                    {/* Medium Quality Section (70-79) */}
                    <div className="bg-slate-800/60 hover:bg-slate-700/50 rounded-md p-3 border border-slate-700/30 transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-slate-900/40">
                      <div className="flex items-center mb-2">
                        <div className="flex items-center space-x-1.5">
                          <div className="h-2.5 w-2.5 rounded-full" style={{ backgroundColor: '#f1c40f' }}></div>
                          <span className="text-xs font-medium text-slate-300">Fair (65–74)</span>
                        </div>
                        <span className="ml-auto text-xs text-slate-300 font-semibold">
                          {(() => {
                            const mediumQualityAssets = portfolio.assets.filter(asset => asset.aiScore >= 70 && asset.aiScore < 80);
                            return mediumQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0).toFixed(2);
                          })()}%
                        </span>
                      </div>
                      {/* Progress bar for medium quality category */}
                      <div className="h-2 w-full bg-slate-700/30 rounded-full mt-1 mb-2 overflow-hidden">
                        <div
                          className="h-full rounded-full animate-grow-width"
                          style={{
                            backgroundColor: '#64748b', // Neutral slate-500 for percentage display
                            '--target-width': `${(() => {
                              const mediumQualityAssets = portfolio.assets.filter(asset => asset.aiScore >= 65 && asset.aiScore < 75);
                              return mediumQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0);
                            })()}%`,
                            animationDelay: '0.3s'
                          } as React.CSSProperties}
                        />
                      </div>
                      <div className="space-y-1.5 mt-2">
                        {portfolio.assets.filter(asset => asset.aiScore >= 65 && asset.aiScore < 75).map((asset, index) => (
                          <div
                            key={`fair-quality-${index}`}
                            className="flex items-center justify-between py-1.5 border-b border-slate-700/20 last:border-0 hover:bg-slate-800/40 transition-colors duration-300 rounded-sm"
                          >
                            <div className="flex items-center space-x-2">
                              <CoinLogo symbol={asset.symbol} size="sm" className="w-5 h-5" imageUrl={asset.logoUrl} />
                              <div className="flex flex-col">
                                <span className="text-xs font-medium text-white">{asset.symbol}</span>
                                <span className="text-[10px] text-slate-400 leading-tight">{asset.name}</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-xs font-medium text-slate-300">{asset.allocation}%</span>
                              {/* ScoreGauge - Fair Quality Fallback */}
                              <div className="flex items-center justify-center">
                                <div className={cn(
                                  "w-6 h-6 rounded-full border-2 flex items-center justify-center",
                                  asset.aiScore >= 90 ? "border-emerald-500 bg-emerald-500/20" :
                                  asset.aiScore >= 75 ? "border-blue-500 bg-blue-500/20" :
                                  asset.aiScore >= 65 ? "border-yellow-500 bg-yellow-500/20" :
                                  asset.aiScore >= 50 ? "border-orange-500 bg-orange-500/20" :
                                  "border-red-500 bg-red-500/20"
                                )}>
                                  <span className="text-xs font-bold text-white">{asset.aiScore}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                        {portfolio.assets.filter(asset => asset.aiScore >= 65 && asset.aiScore < 75).length === 0 && (
                          <div className="text-xs text-slate-500 italic text-center py-2">No fair quality projects in portfolio</div>
                        )}
                      </div>
                    </div>

                    {/* Lower Quality Section (0-69) */}
                    <div className="bg-slate-800/60 hover:bg-slate-700/50 rounded-md p-3 border border-slate-700/30 transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-slate-900/40">
                      <div className="flex items-center mb-2">
                        <div className="flex items-center space-x-1.5">
                          <div className="h-2.5 w-2.5 rounded-full" style={{ backgroundColor: '#e74c3c' }}></div>
                          <span className="text-xs font-medium text-slate-300">Lower (0–64)</span>
                        </div>
                        <span className="ml-auto text-xs text-slate-300 font-semibold">
                          {(() => {
                            const lowerQualityAssets = portfolio.assets.filter(asset => asset.aiScore < 65);
                            return lowerQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0).toFixed(2);
                          })()}%
                        </span>
                      </div>
                      {/* Progress bar for lower quality category */}
                      <div className="h-2 w-full bg-slate-700/30 rounded-full mt-1 mb-2 overflow-hidden">
                        <div
                          className="h-full rounded-full animate-grow-width"
                          style={{
                            backgroundColor: '#64748b', // Neutral slate-500 for percentage display
                            '--target-width': `${(() => {
                              const lowerQualityAssets = portfolio.assets.filter(asset => asset.aiScore < 65);
                              return lowerQualityAssets.reduce((sum, asset) => sum + asset.allocation, 0);
                            })()}%`,
                            animationDelay: '0.4s'
                          } as React.CSSProperties}
                        />
                      </div>
                      <div className="space-y-1.5 mt-2">
                        {portfolio.assets.filter(asset => asset.aiScore < 65).map((asset, index) => (
                          <div
                            key={`lower-quality-${index}`}
                            className="flex items-center justify-between py-1.5 border-b border-slate-700/20 last:border-0 hover:bg-slate-800/40 transition-colors duration-300 rounded-sm"
                          >
                            <div className="flex items-center space-x-2">
                              <CoinLogo symbol={asset.symbol} size="sm" className="w-5 h-5" imageUrl={asset.logoUrl} />
                              <div className="flex flex-col">
                                <span className="text-xs font-medium text-white">{asset.symbol}</span>
                                <span className="text-[10px] text-slate-400 leading-tight">{asset.name}</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-xs font-medium text-slate-300">{asset.allocation}%</span>
                              {/* ScoreGauge - Lower Quality Fallback */}
                              <div className="flex items-center justify-center">
                                <div className={cn(
                                  "w-6 h-6 rounded-full border-2 flex items-center justify-center",
                                  asset.aiScore >= 90 ? "border-emerald-500 bg-emerald-500/20" :
                                  asset.aiScore >= 75 ? "border-blue-500 bg-blue-500/20" :
                                  asset.aiScore >= 65 ? "border-yellow-500 bg-yellow-500/20" :
                                  asset.aiScore >= 50 ? "border-orange-500 bg-orange-500/20" :
                                  "border-red-500 bg-red-500/20"
                                )}>
                                  <span className="text-xs font-bold text-white">{asset.aiScore}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                        {portfolio.assets.filter(asset => asset.aiScore < 65).length === 0 && (
                          <div className="text-xs text-slate-500 italic text-center py-2">No lower quality projects in portfolio</div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          {/* Investment Balance section */}
          {(selectedMetric === 'investment-balance') && (
            <div className="h-full p-4">
              <div className="flex flex-col">
                {/* Main section heading - Standardized format */}
                <div className="mb-5">
                  <div className="flex items-center gap-1.5 mb-1">
                    <div className="bg-blue-500/10 p-1.5 rounded-md">
                      <BarChart className="h-4 w-4 text-blue-400" />
                    </div>
                    <h2 className="text-white text-lg font-medium">Portfolio Weight</h2>
                  </div>
                </div>
                {/* Portfolio Allocation Donut Chart */}
                <div className="w-full mb-6 bg-gradient-to-b from-slate-800/20 to-slate-900/10 rounded-lg p-4 border border-slate-700/20 backdrop-blur-sm shadow-md">
                  <h3 className="text-lg font-semibold text-white mb-4">Portfolio Allocation</h3>
                  <div className="h-[300px] relative">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={[
                            {
                              name: "Top 5 Assets",
                              value: portfolio.assets
                                .sort((a, b) => b.allocation - a.allocation)
                                .slice(0, 5)
                                .reduce((sum, asset) => sum + asset.allocation, 0),
                              color: (() => {
                                const top5Pct = portfolio.assets
                                  .sort((a, b) => b.allocation - a.allocation)
                                  .slice(0, 5)
                                  .reduce((sum, asset) => sum + asset.allocation, 0);
                                // Use neutral blue shades instead of evaluative colors
                                if (top5Pct < 50) return "#3b82f6"; // Standard blue for low concentration
                                if (top5Pct < 65) return "#3173dc"; // Slightly darker blue for moderate concentration
                                if (top5Pct < 80) return "#2563eb"; // Darker blue for high concentration
                                return "#1d4ed8";                   // Darkest blue for highest concentration
                              })()
                            },
                            {
                              name: "Rest of Portfolio",
                              value: 100 - portfolio.assets
                                .sort((a, b) => b.allocation - a.allocation)
                                .slice(0, 5)
                                .reduce((sum, asset) => sum + asset.allocation, 0),
                              color: "#93c5fd" // Light blue for rest of portfolio
                            }
                          ]}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={120}
                          innerRadius={80}
                          dataKey="value"
                          paddingAngle={4}
                          cornerRadius={4}
                          animationBegin={200}
                          animationDuration={1200}
                          animationEasing="ease-out"
                        >
                          {[
                            {
                              name: "Top 5 Assets",
                              value: portfolio.assets
                                .sort((a, b) => b.allocation - a.allocation)
                                .slice(0, 5)
                                .reduce((sum, asset) => sum + asset.allocation, 0),
                              color: (() => {
                                const top5Pct = portfolio.assets
                                  .sort((a, b) => b.allocation - a.allocation)
                                  .slice(0, 5)
                                  .reduce((sum, asset) => sum + asset.allocation, 0);
                                // Use neutral blue shades instead of evaluative colors
                                if (top5Pct < 50) return "#3b82f6"; // Standard blue for low concentration
                                if (top5Pct < 65) return "#3173dc"; // Slightly darker blue for moderate concentration
                                if (top5Pct < 80) return "#2563eb"; // Darker blue for high concentration
                                return "#1d4ed8";                   // Darkest blue for highest concentration
                              })()
                            },
                            {
                              name: "Rest of Portfolio",
                              value: 100 - portfolio.assets
                                .sort((a, b) => b.allocation - a.allocation)
                                .slice(0, 5)
                                .reduce((sum, asset) => sum + asset.allocation, 0),
                              color: "#93c5fd" // Light blue for rest of portfolio
                            }
                          ].map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={entry.color}
                              stroke="#0f172a"
                              strokeWidth={1}
                            />
                          ))}
                        </Pie>
                        <RechartsTooltip
                          contentStyle={{
                            backgroundColor: '#1e293b',
                            border: '1px solid #334155',
                            borderRadius: '8px',
                            padding: '8px'
                          }}
                          labelStyle={{ color: '#e2e8f0', fontWeight: 'bold' }}
                          itemStyle={{ color: '#94a3b8' }}
                        />
                        <Legend
                          layout="horizontal"
                          align="center"
                          verticalAlign="bottom"
                          iconSize={10}
                          iconType="circle"
                        />
                      </PieChart>
                    </ResponsiveContainer>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                      {(() => {
                        const top5Pct = portfolio.assets
                          .sort((a, b) => b.allocation - a.allocation)
                          .slice(0, 5)
                          .reduce((sum, asset) => sum + asset.allocation, 0);
                        const getRatingLabel = (percentage: number): string => {
                          if (percentage < 50) return "Well Balanced";
                          if (percentage < 65) return "Moderately Balanced";
                          if (percentage < 80) return "Average Balance";
                          return "Highly Concentrated";
                        };
                        const getRatingColor = (percentage: number): string => {
                          // Use consistent blue color scheme for neutral visualization
                          if (percentage < 50) return "text-blue-400";    // Standard blue
                          if (percentage < 65) return "text-blue-400";    // Same blue for consistency
                          if (percentage < 80) return "text-blue-400";    // Same blue for consistency
                          return "text-blue-400";                         // Same blue for consistency
                        };
                        return (
                          <>
                            <p className={`text-xl font-semibold ${getRatingColor(top5Pct)}`}>{top5Pct.toFixed(2)}%</p>
                            <p className="text-xs text-slate-400">{getRatingLabel(top5Pct)}</p>
                          </>
                        );
                      })()}
                    </div>
                  </div>
                </div>
                {/* Your Portfolio Breakdown by Asset Concentration */}
                <div className="mt-6">
                  <h3 className="text-sm font-medium text-white mb-4">Your Portfolio Breakdown by Asset Concentration</h3>
                  <div className="space-y-3">
                    {[
                    {
                      name: "Largest Assets",
                      value: (() => {
                        const sorted = [...(portfolio.assets || [])].sort((a, b) => b.allocation - a.allocation);
                        const largest = sorted.filter(asset => asset.allocation >= 10);
                        return largest.reduce((sum, asset) => sum + asset.allocation, 0);
                      })(),
                      color: "#1d4ed8", // darkest blue for largest concentration
                      assets: (() => {
                        const sorted = [...(portfolio.assets || [])].sort((a, b) => b.allocation - a.allocation);
                        const largest = sorted.filter(asset => asset.allocation >= 10);
                        return largest.map(asset => ({
                          symbol: asset.symbol,
                          name: asset.name,
                          allocation: asset.allocation
                        }));
                      })()
                    },
                    {
                      name: "Moderate Allocations",
                      value: (() => {
                        const sorted = [...(portfolio.assets || [])].sort((a, b) => b.allocation - a.allocation);
                        const moderate = sorted.filter(asset => asset.allocation >= 5 && asset.allocation < 10);
                        return moderate.reduce((sum, asset) => sum + asset.allocation, 0);
                      })(),
                      color: "#2563eb", // darker blue for moderate concentration
                      assets: (() => {
                        const sorted = [...(portfolio.assets || [])].sort((a, b) => b.allocation - a.allocation);
                        const moderate = sorted.filter(asset => asset.allocation >= 5 && asset.allocation < 10);
                        return moderate.map(asset => ({
                          symbol: asset.symbol,
                          name: asset.name,
                          allocation: asset.allocation
                        }));
                      })()
                    },
                    {
                      name: "Small Allocations",
                      value: (() => {
                        const sorted = [...(portfolio.assets || [])].sort((a, b) => b.allocation - a.allocation);
                        const small = sorted.filter(asset => asset.allocation > 0 && asset.allocation < 5);
                        return small.reduce((sum, asset) => sum + asset.allocation, 0);
                      })(),
                      color: "#3b82f6", // blue-500
                      assets: (() => {
                        const sorted = [...(portfolio.assets || [])].sort((a, b) => b.allocation - a.allocation);
                        const small = sorted.filter(asset => asset.allocation > 0 && asset.allocation < 5);
                        return small.map(asset => ({
                          symbol: asset.symbol,
                          name: asset.name,
                          allocation: asset.allocation
                        }));
                      })()
                    }
                  ].map((category, index) => (
                      <div key={index} className="bg-slate-800/40 rounded-md p-3 border border-slate-700/30">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="text-sm font-medium text-white">{category.name}</h4>
                          <span className="text-sm font-semibold text-white">{category.value.toFixed(1)}%</span>
                        </div>
                        <div className="h-2 bg-slate-700/50 rounded-full overflow-hidden mb-2">
                          <div
                            className="h-full rounded-full"
                            style={{
                              width: `${category.value}%`,
                              backgroundColor: '#64748b' // Neutral slate-500 for percentage display
                            }}
                          />
                        </div>
                        <div className="space-y-1">
                          {category.assets.slice(0, 3).map((asset, idx) => (
                            <div key={idx} className="flex justify-between text-xs">
                              <span className="text-slate-400">{asset.symbol}</span>
                              <span className="text-slate-300">{asset.allocation.toFixed(1)}%</span>
                            </div>
                          ))}
                          {category.assets.length > 3 && (
                            <div className="text-xs text-slate-500">
                              +{category.assets.length - 3} more
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        {/* Right Score Container - 30% width - Hidden for coinscore tab */}
        {selectedMetric !== 'coinscore' && (
          <div className="w-full lg:w-[30%] h-auto">
            {selectedMetric === 'investment-balance' ? (
            <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4 h-full flex flex-col shadow-lg">
              {/* Portfolio Weight Score Card - matching dashboard design */}
              <Card
                className="bg-slate-800/90 border-slate-700/40 hover:bg-slate-800/80 transition-all cursor-pointer
                         shadow-md shadow-slate-900/40 hover:shadow-lg hover:shadow-slate-900/50
                         transform hover:-translate-y-1 group mb-4"
                onClick={() => setShowMethodologyModal('Portfolio Weight')}
              >
                <CardContent className="p-5">
                  <div className="flex items-center gap-2 mb-3">
                    <div className={cn("p-1.5 rounded-md", getScoreStatus(68).bgColor)}>
                      <BarChart2 className="h-4 w-4" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-white">Portfolio Weight</h4>
                    </div>
                    <ChevronRight className="h-4 w-4 text-slate-400 transition-colors group-hover:text-slate-200" />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-baseline justify-between">
                      <span className={cn("text-2xl font-bold", getScoreStatus(68).color)}>
                        68
                      </span>
                      <span className={cn("text-xs", getScoreStatus(68).color)}>
                        {getScoreStatus(68).label}
                      </span>
                    </div>

                    <Progress
                      value={68}
                      className="h-1.5 bg-slate-700/50"
                    />
                  </div>
                </CardContent>
              </Card>
              <h4 className="text-xs font-medium text-slate-300 mb-2 flex items-center gap-2">
                <BarChart2 className="h-3.5 w-3.5 text-blue-400" aria-hidden="true" />
                <span>Summary</span>
              </h4>
              <div className="mb-4 bg-slate-800/50 rounded-lg border border-slate-700/30 p-3 shadow-md">
                <p className="text-xs text-slate-400 leading-relaxed">
                  {(() => {
                    const sortedAssets = [...(portfolio.assets || [])].sort((a, b) => b.allocation - a.allocation);
                    const top5Assets = sortedAssets.slice(0, 5);
                    const top5Combined = top5Assets.reduce((sum, asset) => sum + asset.allocation, 0);
                    return `Your top 5 assets represent ${top5Combined.toFixed(2)}% of your portfolio.
                    ${top5Combined < 50
                      ? "This excellent distribution minimizes concentration risk."
                      : top5Combined < 65
                        ? "This distribution provides good balance with moderate concentration risk."
                        : top5Combined < 80
                          ? "This creates average balance with elevated concentration risk."
                          : "This high concentration increases vulnerability to market volatility."
                    } Optimal portfolios typically have less than 50% in their top 5 assets.`
                  })()}
                </p>
              </div>
              {/* Recommendations Section - Temporarily disabled as component is not available */}
              <div className="bg-slate-800/50 rounded-lg border border-slate-700/30 p-4">
                <h4 className="text-sm font-medium text-white mb-3">Recommendations</h4>
                <div className="space-y-2 text-sm text-slate-300">
                  {(() => {
                    const sortedAssets = [...(portfolio.assets || [])].sort((a, b) => b.allocation - a.allocation);
                    const top5Assets = sortedAssets.slice(0, 5);
                    const top5Combined = top5Assets.reduce((sum, asset) => sum + asset.allocation, 0);

                    if (top5Combined < 50) {
                      return [
                        "✓ Maintain your excellent diversification approach",
                        "• Consider exploring new sectors for small additional positions",
                        "ℹ Your well-balanced portfolio offers resilience against market volatility"
                      ];
                    } else if (top5Combined < 65) {
                      return [
                        "✓ Your allocation balance is good",
                        "• Gradually expand into 2-3 additional assets with modest allocations",
                        "ℹ Further diversification will strengthen your portfolio"
                      ];
                    } else if (top5Combined < 80) {
                      return [
                        "⚠ Reduce concentration by reallocating 10-15% from largest holdings",
                        "• Distribute funds to smaller positions or new assets",
                        "ℹ Aim for less than 65% allocation in your top 5 assets"
                      ];
                    } else {
                      return [
                        "⚠ Reduce top 5 positions to below 70% combined allocation",
                        "• Distribute funds across at least 5-7 additional assets",
                        "ℹ Your portfolio needs immediate diversification to reduce risk"
                      ];
                    }
                  })().map((rec, idx) => (
                    <div key={idx} className="flex items-start gap-2">
                      <span className="text-xs">{rec}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            getScoreCardContent()
          )}
        </div>
        )}
      </div>

      {/* Unified Methodology Modal - Serves both dashboard and tab metric cards */}
      {showMethodologyModal && (
        <Dialog open={!!showMethodologyModal} onOpenChange={() => setShowMethodologyModal(null)}>
          <DialogContent className="bg-slate-900/95 border-slate-700/40 text-white max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-xl font-semibold flex items-center gap-3">
                {(() => {
                  const metric = methodologyData[showMethodologyModal];
                  if (!metric) return null;
                  const Icon = metric.icon;
                  const metricScore = metric.score ?? 0;
                  const status = metricScore >= 90 ? 'Excellent' :
                               metricScore >= 75 ? 'Positive' :
                               metricScore >= 65 ? 'Average' : 'Weak';
                  const statusColor = metricScore >= 90 ? '#22c55e' :
                                    metricScore >= 75 ? '#00E5E5' :
                                    metricScore >= 65 ? '#fbbf24' : '#ef4444';

                  return (
                    <>
                      <Icon className="h-5 w-5" />
                      <span>{showMethodologyModal}</span>
                      <Badge
                        className="ml-auto"
                        style={{
                          backgroundColor: `${statusColor}20`,
                          color: statusColor,
                          borderColor: statusColor
                        }}
                      >
                        {status}
                      </Badge>
                    </>
                  );
                })()}
              </DialogTitle>
            </DialogHeader>

            {(() => {
              const metric = methodologyData[showMethodologyModal];
              if (!metric) return null;
              const metricScore = metric.score ?? 0;

              return (
                <div className="space-y-6 mt-6">
                  {/* Score Display */}
                  <div className="text-center py-4 bg-slate-800/50 rounded-lg">
                    <div className="text-3xl font-bold mb-1" style={{
                      color: metricScore >= 90 ? '#22c55e' :
                            metricScore >= 75 ? '#00E5E5' :
                            metricScore >= 65 ? '#fbbf24' : '#ef4444'
                    }}>
                      {metricScore} / 100
                    </div>
                    <div className="text-sm text-slate-400">{showMethodologyModal.toUpperCase()} SCORE</div>
                  </div>

                  {/* What Are We Scoring */}
                  <div>
                    <h3 className="text-base font-semibold mb-3 flex items-center gap-2">
                      <Target className="h-4 w-4 text-blue-400" />
                      What Are We Scoring?
                    </h3>
                    <p className="text-sm text-slate-300 leading-relaxed">
                      {metric.description}
                    </p>
                  </div>

                  {/* Why Is This Important */}
                  <div>
                    <h3 className="text-base font-semibold mb-3 flex items-center gap-2">
                      <Info className="h-4 w-4 text-yellow-400" />
                      Why Is This Important?
                    </h3>
                    <ul className="space-y-2">
                      {metric.importance.map((item: string, idx: number) => (
                        <li key={idx} className="flex items-start gap-2 text-sm text-slate-300">
                          <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Metric Weighting Table - Only for Overall Portfolio */}
                  {metric.weightingTable && (
                    <div>
                      <h3 className="text-base font-semibold mb-3 flex items-center gap-2">
                        <BarChart className="h-4 w-4 text-indigo-400" />
                        Metric Weighting Breakdown
                      </h3>
                      <div className="bg-slate-800/50 rounded-lg overflow-hidden">
                        <table className="w-full text-sm">
                          <thead className="bg-slate-700/50">
                            <tr>
                              <th className="text-left px-4 py-3 font-medium text-slate-300">Metric</th>
                              <th className="text-center px-4 py-3 font-medium text-slate-300">Score</th>
                              <th className="text-center px-4 py-3 font-medium text-slate-300">Weight</th>
                              <th className="text-right px-4 py-3 font-medium text-slate-300">Contribution</th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-slate-700/50">
                            {metric.weightingTable.map((row, idx) => (
                              <tr key={idx} className="hover:bg-slate-700/30">
                                <td className="px-4 py-3 text-slate-300">{row.metric}</td>
                                <td className="text-center px-4 py-3 font-medium">{row.score} pts</td>
                                <td className="text-center px-4 py-3 text-slate-400">{row.weight}</td>
                                <td className="text-right px-4 py-3 font-medium text-blue-400">{row.contribution.toFixed(1)}</td>
                              </tr>
                            ))}
                            <tr className="bg-slate-700/30 font-semibold">
                              <td className="px-4 py-3 text-slate-200">TOTAL WEIGHTED SCORE</td>
                              <td colSpan={2}></td>
                              <td className="text-right px-4 py-3 text-cyan-400 text-base">89.3</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {/* Scoring Levels */}
                  <div>
                    <h3 className="text-base font-semibold mb-3 flex items-center gap-2">
                      <BarChart3 className="h-4 w-4 text-purple-400" />
                      Scoring Levels
                    </h3>
                    <div className="space-y-2">
                      {metric.scoringLevels.map((level: { range: string; status: string; color: string }, idx: number) => (
                        <div key={idx} className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: level.color }}
                            />
                            <span className="text-sm font-medium" style={{ color: level.color }}>
                              {level.status}
                            </span>
                          </div>
                          <span className="text-sm text-slate-400">{level.range}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })()}
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
}

// Add default export for React.lazy() compatibility
export default EnhancedPortfolioAllocationChart;
